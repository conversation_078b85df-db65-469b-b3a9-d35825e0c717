{"info": {"name": "MyProfile Product API Collection", "description": "Comprehensive collection of MyProfile product-related API endpoints for frontend and mobile app development", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{accessToken}}", "type": "string"}]}, "variable": [{"key": "baseUrl", "value": "https://app.getmyprofile.online/api", "type": "string"}, {"key": "localUrl", "value": "http://localhost:5000/api", "type": "string"}, {"key": "accessToken", "value": "", "type": "string"}, {"key": "profileId", "value": "", "type": "string"}], "item": [{"name": "🔍 Search & Discovery", "item": [{"name": "Search Profiles", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text", "disabled": true}], "url": {"raw": "{{baseUrl}}/search/profiles?query=john&page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["search", "profiles"], "query": [{"key": "query", "value": "john", "description": "Search query string (required)"}, {"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Results per page (default: 10)"}, {"key": "filters", "value": "", "description": "JSON string with additional filters", "disabled": true}]}, "description": "Search for user profiles across the platform. Authentication is optional but provides better results."}}]}, {"name": "🏪 Product Pool Management", "item": [{"name": "Get Available Product Pools", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-pools?productType=cards&status=ready_for_sale&hasAvailableProducts=true", "host": ["{{baseUrl}}"], "path": ["product-pools"], "query": [{"key": "productType", "value": "cards", "description": "Filter by product type (cards, wearables, stationaries, stickers, bundles, customize, accessories)"}, {"key": "status", "value": "ready_for_sale", "description": "Filter by status"}, {"key": "hasAvailableProducts", "value": "true", "description": "Boolean filter for availability"}, {"key": "manufacturerBid", "value": "", "description": "Filter by manufacturer", "disabled": true}, {"key": "batchNumber", "value": "", "description": "Filter by batch number", "disabled": true}]}, "description": "Get all product pools that are ready to buy. This is a public endpoint."}}, {"name": "Search Product Pools", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-pools/search?q=smart card", "host": ["{{baseUrl}}"], "path": ["product-pools", "search"], "query": [{"key": "q", "value": "smart card", "description": "Search query (minimum 2 characters)"}]}, "description": "Search through product pools. This is a public endpoint."}}, {"name": "Get Product Pool Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-pools/POOL-001", "host": ["{{baseUrl}}"], "path": ["product-pools", "POOL-001"]}, "description": "Get detailed information about a specific product pool. This is a public endpoint."}}, {"name": "Get Pool Statistics", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-pools/POOL-001/stats", "host": ["{{baseUrl}}"], "path": ["product-pools", "POOL-001", "stats"]}, "description": "Get statistical information about a product pool. This is a public endpoint."}}, {"name": "Get Low Stock Pools", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/product-pools/low-stock?threshold=10", "host": ["{{baseUrl}}"], "path": ["product-pools", "low-stock"], "query": [{"key": "threshold", "value": "10", "description": "Stock threshold (default: 10)"}]}, "description": "Get product pools that are running low on inventory. Requires authentication."}}, {"name": "Allocate Product from Pool", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"customerId\": \"customer_123456\"\n}"}, "url": {"raw": "{{baseUrl}}/product-pools/POOL-001/allocate", "host": ["{{baseUrl}}"], "path": ["product-pools", "POOL-001", "allocate"]}, "description": "Allocate a product from a pool for purchase. Requires authentication."}}]}, {"name": "🏷️ QR Code Management", "item": [{"name": "Get All QR Codes", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/qrcodes?page=1&limit=10", "host": ["{{baseUrl}}"], "path": ["qrcodes"], "query": [{"key": "page", "value": "1", "description": "Page number"}, {"key": "limit", "value": "10", "description": "Results per page"}, {"key": "productId", "value": "", "description": "Filter by product ID", "disabled": true}, {"key": "status", "value": "", "description": "Filter by status", "disabled": true}]}, "description": "Get all QR codes with filtering options. Requires authentication."}}, {"name": "Get QR Code by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/qrcodes/qr_123456", "host": ["{{baseUrl}}"], "path": ["qrcodes", "qr_123456"]}, "description": "Retrieve QR code information by its ID. Requires authentication."}}, {"name": "Record QR Code Scan", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"location\": {\n    \"lat\": 40.7128,\n    \"lng\": -74.0060\n  },\n  \"deviceInfo\": {\n    \"userAgent\": \"Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)\",\n    \"platform\": \"iOS\",\n    \"vendor\": \"Apple\",\n    \"language\": \"en-US\",\n    \"screenSize\": {\n      \"width\": 375,\n      \"height\": 812\n    }\n  },\n  \"timestamp\": \"2024-01-15T10:30:00.000Z\"\n}"}, "url": {"raw": "{{baseUrl}}/qrcodes/qr_123456/scan", "host": ["{{baseUrl}}"], "path": ["qrcodes", "qr_123456", "scan"]}, "description": "Record a scan event for analytics. Requires authentication."}}, {"name": "Get QR Code Statistics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/qrcodes/qr_123456/stats", "host": ["{{baseUrl}}"], "path": ["qrcodes", "qr_123456", "stats"]}, "description": "Get scan statistics for a QR code. Requires authentication."}}, {"name": "Get QR Codes for Product", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/qrcodes/product/MYC-BATCH-001-0001", "host": ["{{baseUrl}}"], "path": ["qrcodes", "product", "MYC-BATCH-001-0001"]}, "description": "Get all QR codes associated with a specific product. Requires authentication."}}, {"name": "Generate Bulk QR Codes", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"qrCodes\": [\n    {\n      \"productId\": \"MYC-BATCH-001-0001\",\n      \"qrCodeData\": \"https://getmyprofile.online/activate/A1B2C3D4\",\n      \"style\": {\n        \"size\": 300,\n        \"errorCorrectionLevel\": \"M\"\n      }\n    }\n  ]\n}"}, "url": {"raw": "{{baseUrl}}/qrcodes/bulk", "host": ["{{baseUrl}}"], "path": ["qrcodes", "bulk"]}, "description": "Generate multiple QR codes at once. Requires authentication."}}, {"name": "Delete QR Code", "request": {"method": "DELETE", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/qrcodes/qr_123456", "host": ["{{baseUrl}}"], "path": ["qrcodes", "qr_123456"]}, "description": "Delete a QR code. Requires authentication."}}]}, {"name": "📦 In-Use Product Management", "item": [{"name": "Get User's Products", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/in-use-products?page=1&limit=10&sort=createdAt&order=desc", "host": ["{{baseUrl}}"], "path": ["in-use-products"], "query": [{"key": "page", "value": "1", "description": "Page number (default: 1)"}, {"key": "limit", "value": "10", "description": "Items per page (default: 10)"}, {"key": "sort", "value": "createdAt", "description": "Sort field (default: createdAt)"}, {"key": "order", "value": "desc", "description": "Sort order (asc or desc, default: desc)"}, {"key": "search", "value": "", "description": "Search query", "disabled": true}, {"key": "status", "value": "", "description": "Filter by status (active, inactive, pending)", "disabled": true}, {"key": "category", "value": "", "description": "Filter by category", "disabled": true}]}, "description": "Retrieve products owned by the authenticated user. Requires authentication."}}, {"name": "Create In-Use Product", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"My Smart Card\",\n  \"material\": \"Premium PVC\",\n  \"category\": \"cards\",\n  \"productId\": \"MYC-BATCH-001-0001\",\n  \"orderId\": \"MO-001\",\n  \"profileId\": \"{{profileId}}\",\n  \"activator\": {\n    \"name\": \"<PERSON> Doe\",\n    \"date\": \"2024-01-15T14:30:00.000Z\"\n  },\n  \"color\": \"Black\",\n  \"style\": \"Classic\",\n  \"size\": \"Standard\",\n  \"description\": \"Premium smart card with NFC technology\",\n  \"status\": \"active\",\n  \"visibility\": true\n}"}, "url": {"raw": "{{baseUrl}}/in-use-products", "host": ["{{baseUrl}}"], "path": ["in-use-products"]}, "description": "Create a new in-use product record (typically after activation). Requires authentication."}}, {"name": "Get Product by ID", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/in-use-products/product_123", "host": ["{{baseUrl}}"], "path": ["in-use-products", "product_123"]}, "description": "Retrieve a specific in-use product by its ID. Requires authentication."}}, {"name": "Update Product", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"name\": \"My Updated Smart Card\",\n  \"description\": \"Updated premium smart card with enhanced features\",\n  \"visibility\": false\n}"}, "url": {"raw": "{{baseUrl}}/in-use-products/product_123", "host": ["{{baseUrl}}"], "path": ["in-use-products", "product_123"]}, "description": "Update an in-use product's information. Requires authentication."}}, {"name": "Update Product Status", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"inactive\"\n}"}, "url": {"raw": "{{baseUrl}}/in-use-products/product_123/status", "host": ["{{baseUrl}}"], "path": ["in-use-products", "product_123", "status"]}, "description": "Update the status of an in-use product. Requires authentication."}}, {"name": "Toggle Product Visibility", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/in-use-products/product_123/visibility", "host": ["{{baseUrl}}"], "path": ["in-use-products", "product_123", "visibility"]}, "description": "Toggle the visibility status of a product. Requires authentication."}}]}, {"name": "⭐ Product Reviews", "item": [{"name": "Get Product Reviews", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-reviews/product/product_123?limit=10&skip=0&sortBy=helpful&filterBy=all", "host": ["{{baseUrl}}"], "path": ["product-reviews", "product", "product_123"], "query": [{"key": "limit", "value": "10", "description": "Number of reviews per page (1-100, default: 10)"}, {"key": "skip", "value": "0", "description": "Number of reviews to skip (default: 0)"}, {"key": "sortBy", "value": "helpful", "description": "Sort order (helpful, recent, rating)"}, {"key": "filterBy", "value": "all", "description": "Filter type (all, verified, withMedia)"}]}, "description": "Get reviews for a specific product. This is a public endpoint."}}, {"name": "Get Product Review Stats", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-reviews/stats/product_123", "host": ["{{baseUrl}}"], "path": ["product-reviews", "stats", "product_123"]}, "description": "Get statistical overview of reviews for a product. This is a public endpoint."}}, {"name": "Create Product Review", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"productId\": \"product_123\",\n  \"rating\": 5,\n  \"title\": \"Great product!\",\n  \"content\": \"Really satisfied with the quality and design. Would recommend to others.\",\n  \"media\": [\n    {\n      \"type\": \"image\",\n      \"url\": \"https://res.cloudinary.com/myprofile/image/upload/v1234567890/reviews/my-photo.jpg\"\n    }\n  ],\n  \"isAnonymous\": false\n}"}, "url": {"raw": "{{baseUrl}}/product-reviews", "host": ["{{baseUrl}}"], "path": ["product-reviews"]}, "description": "Create a new review for a product. Requires authentication."}}, {"name": "Update Product Review", "request": {"method": "PATCH", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": "{\n  \"rating\": 4,\n  \"title\": \"Updated title\",\n  \"content\": \"Updated review content\",\n  \"media\": []\n}"}, "url": {"raw": "{{baseUrl}}/product-reviews/review_123", "host": ["{{baseUrl}}"], "path": ["product-reviews", "review_123"]}, "description": "Update an existing review (owner only). Requires authentication."}}, {"name": "Mark <PERSON> as Helpful", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/product-reviews/review_123/helpful", "host": ["{{baseUrl}}"], "path": ["product-reviews", "review_123", "helpful"]}, "description": "Mark a review as helpful. Requires authentication."}}]}, {"name": "🏭 Manufacturers", "item": [{"name": "Get All Manufacturers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/manufacturers", "host": ["{{baseUrl}}"], "path": ["manufacturers"]}, "description": "Get list of all manufacturers. This is a public endpoint."}}, {"name": "Search Manufacturers", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/manufacturers/search?q=tech", "host": ["{{baseUrl}}"], "path": ["manufacturers", "search"], "query": [{"key": "q", "value": "tech", "description": "Search query (required)"}]}, "description": "Search for manufacturers by name or specialty. This is a public endpoint."}}, {"name": "Get Manufacturer Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/manufacturers/mfg_001", "host": ["{{baseUrl}}"], "path": ["manufacturers", "mfg_001"]}, "description": "Get detailed information about a specific manufacturer. This is a public endpoint."}}]}, {"name": "🏷️ Categories", "item": [{"name": "Get All Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories", "host": ["{{baseUrl}}"], "path": ["categories"]}, "description": "Get all product categories. This is a public endpoint."}}, {"name": "Search Categories", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/search?query=smart", "host": ["{{baseUrl}}"], "path": ["categories", "search"], "query": [{"key": "query", "value": "smart", "description": "Search query (required)"}]}, "description": "Search categories by name and description. This is a public endpoint."}}, {"name": "Get Category Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/categories/cat_001", "host": ["{{baseUrl}}"], "path": ["categories", "cat_001"]}, "description": "Get detailed information about a specific category. This is a public endpoint."}}]}, {"name": "🎨 Materials", "item": [{"name": "Get All Materials", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/materials?category=plastic&grade=premium", "host": ["{{baseUrl}}"], "path": ["materials"], "query": [{"key": "category", "value": "plastic", "description": "Filter by category", "disabled": true}, {"key": "grade", "value": "premium", "description": "Filter by grade", "disabled": true}, {"key": "compatible", "value": "", "description": "Filter compatible materials for product type", "disabled": true}]}, "description": "Get available materials for product configuration. This is a public endpoint."}}, {"name": "Get Material Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/materials/mat_001", "host": ["{{baseUrl}}"], "path": ["materials", "mat_001"]}, "description": "Get detailed information about a specific material. This is a public endpoint."}}]}, {"name": "🎯 Product Variants", "item": [{"name": "Get All Product Variants", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-variants?type=color&category=cards", "host": ["{{baseUrl}}"], "path": ["product-variants"], "query": [{"key": "type", "value": "color", "description": "Filter by variant type (color, style, size)", "disabled": true}, {"key": "category", "value": "cards", "description": "Filter by product category", "disabled": true}]}, "description": "Get available product variants (colors, styles, sizes). This is a public endpoint."}}, {"name": "Get Variant Details", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/product-variants/var_001", "host": ["{{baseUrl}}"], "path": ["product-variants", "var_001"]}, "description": "Get detailed information about a specific variant. This is a public endpoint."}}]}, {"name": "📊 Analytics", "item": [{"name": "Get Product Analytics", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{accessToken}}", "type": "text"}, {"key": "X-Profile-Id", "value": "{{profileId}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/analytics/dashboard/{{profileId}}/products", "host": ["{{baseUrl}}"], "path": ["analytics", "dashboard", "{{profileId}}", "products"]}, "description": "Get analytics data for products owned by a profile. Requires authentication and profile access."}}]}]}