{"id": "myprofile-local-env", "name": "MyProfile Local Development Environment", "values": [{"key": "baseUrl", "value": "http://localhost:5000/api", "type": "default", "enabled": true}, {"key": "accessToken", "value": "", "type": "secret", "enabled": true}, {"key": "refreshToken", "value": "", "type": "secret", "enabled": true}, {"key": "profileToken", "value": "", "type": "secret", "enabled": true}, {"key": "profileId", "value": "", "type": "default", "enabled": true}, {"key": "userId", "value": "", "type": "default", "enabled": true}, {"key": "testProductId", "value": "MYC-BATCH-001-0001", "type": "default", "enabled": true}, {"key": "testPoolId", "value": "POOL-001", "type": "default", "enabled": true}, {"key": "testQRCodeId", "value": "qr_123456", "type": "default", "enabled": true}, {"key": "testInUseProductId", "value": "product_123", "type": "default", "enabled": true}, {"key": "testReviewId", "value": "review_123", "type": "default", "enabled": true}, {"key": "testManufacturerId", "value": "mfg_001", "type": "default", "enabled": true}, {"key": "testCategoryId", "value": "cat_001", "type": "default", "enabled": true}, {"key": "testMaterialId", "value": "mat_001", "type": "default", "enabled": true}, {"key": "testVariantId", "value": "var_001", "type": "default", "enabled": true}], "_postman_variable_scope": "environment"}