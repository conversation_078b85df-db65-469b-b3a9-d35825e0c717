# My Profile Order & Shipping System - Client Documentation

## Overview

The My Profile Order & Shipping System provides customers with comprehensive order tracking, delivery notifications, and shipment management capabilities. This documentation covers all customer-facing features and API endpoints.

## Table of Contents

1. [Customer Features](#customer-features)
2. [Order Tracking](#order-tracking)
3. [Tracking Portal](#tracking-portal)
4. [Notifications](#notifications)
5. [Client API Reference](#client-api-reference)
6. [Integration Examples](#integration-examples)
7. [Mobile Considerations](#mobile-considerations)
8. [Support & Help](#support--help)

## Customer Features

### Core Capabilities

- **Real-time Order Tracking**: Track orders from placement to delivery
- **Multiple Tracking Methods**: Search by order number or tracking number
- **Delivery Notifications**: Email and SMS updates at key milestones
- **Delivery History**: View past orders and delivery confirmations
- **Proactive Notifications**: Automatic updates for delays or exceptions
- **Mobile-Optimized**: Responsive design for all devices
- **Shareable Tracking**: Share tracking links with family/colleagues

### Supported Carriers

- **USPS**: United States Postal Service
- **UPS**: United Parcel Service
- **FedEx**: Federal Express
- **DHL**: DHL Express (International)

### Product Categories

| Product | Price | Typical Shipping | Package Type |
|---------|-------|------------------|--------------|
| Profile Cards | $349.99 | FedEx Express | Rigid Mailer |
| Profile Bands | $99.99 | UPS Ground | Small Box |
| Profile Stickers | $99.99 | USPS Priority | Envelope |
| Key Tags | $10.75 | USPS First Class | Envelope |
| Accessories | Variable | Based on Value | Variable |

## Order Tracking

### Tracking Methods

#### 1. Order Number Tracking
- Format: `MP-YYYY-XXXXXX` (e.g., MP-2024-001234)
- Found in order confirmation email
- Works immediately after order placement
- Provides complete order history

#### 2. Tracking Number Tracking
- Carrier-specific format (varies by carrier)
- Available once order ships
- Provides real-time carrier updates
- Shows detailed delivery timeline

### Order Statuses

| Status | Description | Customer Action |
|--------|-------------|-----------------|
| `pending` | Order received, payment processing | Wait for confirmation |
| `confirmed` | Payment confirmed, preparing for fulfillment | None required |
| `processing` | Items being packed and labeled | None required |
| `shipped` | Package handed to carrier | Track package progress |
| `in_transit` | Package in carrier network | Monitor delivery updates |
| `out_for_delivery` | Package on delivery vehicle | Prepare for delivery |
| `delivered` | Package delivered successfully | Confirm receipt |
| `exception` | Delivery issue occurred | Contact support |
| `cancelled` | Order cancelled | Check refund status |

### Delivery Timeline

#### Standard Timeline
1. **Order Placed** (Day 0)
2. **Processing** (Days 0-1)
3. **Shipped** (Day 1)
4. **Delivery** (Days 2-5, depending on shipping method)

#### Shipping Tiers
- **Lightweight** (<$25): 3-5 business days via USPS
- **Standard** ($25-$99): 2-4 business days via UPS/USPS
- **Premium** ($100-$249): 1-3 business days via FedEx/UPS
- **Bundle** ($250+): 1-2 business days via FedEx Express

## Tracking Portal

### Portal Access

**URL**: `https://getmyprofile.online/track`

#### Search Interface
```html
<!-- Tracking Search Form -->
<form class="tracking-search">
  <input 
    type="text" 
    placeholder="Enter Order Number (MP-2024-001234) or Tracking Number"
    class="tracking-input"
  />
  <button type="submit" class="tracking-button">Track Order</button>
</form>
```

### Portal Features

#### Order Information Display
- Order number and placement date
- Current status with progress indicator
- Estimated delivery date
- Complete item list with quantities
- Shipping address
- Customer service contact options

#### Tracking Timeline
- Chronological list of tracking events
- Location information for each event
- Carrier-specific status descriptions
- Timestamps in customer's local timezone

#### Progress Visualization
- Step-by-step progress stepper
- Visual indicators for completed stages
- Estimated completion times
- Real-time status updates

### Mobile Experience

#### Responsive Design
- Touch-friendly interface elements
- Optimized for screens 320px and wider
- Native app-like experience
- Offline capability for cached tracking data

#### Mobile-Specific Features
- One-tap phone number calling
- GPS-based delivery notifications
- Push notifications (when available)
- Share tracking via native share menu

## Notifications

### Email Notifications

#### Order Confirmation
**Trigger**: Order placed successfully
**Content**: Order details, estimated delivery, tracking instructions

```html
Subject: Your My Profile Order MP-2024-001234 is Confirmed

Hi [Customer Name],

Thank you for your order! Here are the details:

Order Number: MP-2024-001234
Items Ordered:
- 1x Profile Card - $349.99
- 2x Profile Stickers - $199.98

Total: $549.97

Estimated Delivery: [Date]

Track your order: https://getmyprofile.online/track/MP-2024-001234
```

#### Shipping Notification
**Trigger**: Order shipped
**Content**: Tracking number, carrier information, delivery estimate

```html
Subject: Your My Profile Order is On Its Way! 📦

Hi [Customer Name],

Great news! Your order MP-2024-001234 has shipped.

Tracking Number: 1Z999AA1234567890
Carrier: UPS
Estimated Delivery: [Date]

Track your package: https://getmyprofile.online/track/1Z999AA1234567890
```

#### Delivery Notification
**Trigger**: Package delivered
**Content**: Delivery confirmation, receipt instructions

```html
Subject: Your My Profile Order Has Been Delivered! ✅

Hi [Customer Name],

Your order MP-2024-001234 has been delivered successfully.

Delivered: [Timestamp]
Location: [Delivery Address]

If you didn't receive your package, please contact us immediately.
```

#### Exception Notifications
**Trigger**: Delivery exception or delay
**Content**: Issue description, expected resolution, customer action

```html
Subject: Update on Your My Profile Order MP-2024-001234

Hi [Customer Name],

There's been an update on your order delivery:

Status: Delivery Exception
Reason: Address Correction Required
Action Needed: Please contact UPS at 1-800-PICK-UPS

We're working to resolve this quickly. Expected resolution: [Date]
```

### SMS Notifications

#### Opt-in Process
Customers can subscribe to SMS updates during checkout or via tracking portal.

#### Message Examples
```sms
MyProfile: Your order MP-2024-001234 has shipped! Track: getmyprofile.online/t/abc123 UPS: 1Z999AA1234567890

MyProfile: Package out for delivery today for order MP-2024-001234. Track: getmyprofile.online/t/abc123

MyProfile: Delivered! Your order MP-2024-001234 was delivered at 2:15 PM. Questions? Reply HELP
```

#### SMS Commands
- `STOP`: Unsubscribe from SMS notifications
- `HELP`: Get customer service information
- `STATUS`: Get current order status
- `TRACK [order#]`: Get tracking link

### Push Notifications (Future Feature)

#### Web Push (Progressive Web App)
```javascript
// Service Worker Registration
if ('serviceWorker' in navigator && 'PushManager' in window) {
  navigator.serviceWorker.register('/sw.js')
    .then(registration => {
      return registration.pushManager.subscribe({
        userVisibleOnly: true,
        applicationServerKey: urlBase64ToUint8Array(publicVapidKey)
      });
    });
}
```

## Client API Reference

### Public Tracking Endpoints

#### Track Order
```http
GET /api/orders/track/{identifier}
```

**Parameters:**
- `identifier`: Order number (MP-2024-001234) or tracking number
- `email` (query, optional): Customer email for verification

**Response:**
```json
{
  "success": true,
  "data": {
    "order": {
      "orderNumber": "MP-2024-001234",
      "trackingNumber": "1Z999AA1234567890",
      "carrier": "UPS",
      "status": "in_transit",
      "estimatedDelivery": "2024-01-15T17:00:00Z",
      "items": [
        {
          "name": "Profile Card",
          "quantity": 1,
          "sku": "PC-001"
        }
      ],
      "shipping": {
        "address": {
          "street": "123 Main St",
          "city": "Anytown",
          "state": "CA",
          "zipCode": "90210",
          "country": "US"
        },
        "method": "UPS Ground",
        "tier": "premium"
      },
      "trackingEvents": [
        {
          "timestamp": "2024-01-12T10:00:00Z",
          "status": "shipped",
          "description": "Package picked up",
          "location": {
            "city": "Los Angeles",
            "state": "CA",
            "country": "US"
          },
          "carrier": "UPS"
        }
      ],
      "placedAt": "2024-01-10T14:30:00Z",
      "shippedAt": "2024-01-12T10:00:00Z"
    }
  }
}
```

#### Get Tracking Updates
```http
GET /api/orders/track/{identifier}/updates
```

**Purpose**: Get real-time tracking updates from carrier
**Response**: Latest tracking events with carrier sync timestamp

#### Get Customer Orders
```http
GET /api/orders/customer/{email}
```

**Parameters:**
- `email`: Customer email address
- `limit` (query, optional): Number of orders to return (default: 10)
- `page` (query, optional): Page number (default: 1)

**Response:**
```json
{
  "success": true,
  "data": {
    "orders": [
      {
        "orderNumber": "MP-2024-001234",
        "status": "delivered",
        "placedAt": "2024-01-10T14:30:00Z",
        "estimatedDelivery": "2024-01-15T17:00:00Z",
        "trackingNumber": "1Z999AA1234567890",
        "carrier": "UPS",
        "items": [
          {
            "name": "Profile Card",
            "quantity": 1,
            "price": 349.99
          }
        ],
        "totalAmount": 349.99
      }
    ],
    "pagination": {
      "current": 1,
      "total": 5,
      "count": 10,
      "totalOrders": 47
    }
  }
}
```

#### Subscribe to Notifications
```http
POST /api/orders/track/{identifier}/notifications
```

**Body:**
```json
{
  "email": "<EMAIL>",
  "phone": "+1234567890",
  "notificationTypes": ["shipped", "delivered", "exception"]
}
```

**Response:**
```json
{
  "success": true,
  "message": "Successfully subscribed to tracking notifications",
  "data": {
    "orderNumber": "MP-2024-001234",
    "notifications": {
      "email": "<EMAIL>",
      "phone": "+1234567890",
      "types": ["shipped", "delivered", "exception"],
      "subscribedAt": "2024-01-10T15:00:00Z"
    }
  }
}
```

#### Get Delivery Confirmation
```http
GET /api/orders/track/{identifier}/delivery
```

**Purpose**: Get detailed delivery confirmation information
**Requirement**: Order must be in 'delivered' status

**Response:**
```json
{
  "success": true,
  "data": {
    "delivery": {
      "orderNumber": "MP-2024-001234",
      "trackingNumber": "1Z999AA1234567890",
      "carrier": "UPS",
      "deliveredAt": "2024-01-15T14:30:00Z",
      "deliveryLocation": {
        "city": "Anytown",
        "state": "CA",
        "country": "US",
        "zipCode": "90210"
      },
      "deliveryDescription": "Left at front door",
      "recipientName": "John Smith",
      "shippingAddress": {
        "street": "123 Main St",
        "city": "Anytown",
        "state": "CA",
        "zipCode": "90210",
        "country": "US"
      },
      "items": [
        {
          "name": "Profile Card",
          "quantity": 1,
          "sku": "PC-001"
        }
      ]
    }
  }
}
```

### Error Handling

#### Standard Error Response
```json
{
  "success": false,
  "error": {
    "code": "ORDER_NOT_FOUND",
    "message": "Order not found",
    "details": "No order found with identifier: MP-2024-999999"
  }
}
```

#### Common Error Codes
- `ORDER_NOT_FOUND`: Order/tracking number not found
- `INVALID_IDENTIFIER`: Invalid order number or tracking number format
- `EMAIL_MISMATCH`: Email doesn't match order (when email verification used)
- `ORDER_NOT_SHIPPED`: Tracking not available (order not shipped yet)
- `CARRIER_UNAVAILABLE`: Carrier API temporarily unavailable
- `RATE_LIMITED`: Too many requests (429 status)

## Integration Examples

### JavaScript Integration

#### Basic Tracking Widget
```javascript
class MyProfileTracker {
  constructor(apiBaseUrl = 'https://getmyprofile.online/api') {
    this.baseUrl = apiBaseUrl;
  }

  async trackOrder(identifier, email = null) {
    try {
      const url = new URL(`${this.baseUrl}/orders/track/${identifier}`);
      if (email) {
        url.searchParams.append('email', email);
      }

      const response = await fetch(url);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error.message);
      }

      return data.data.order;
    } catch (error) {
      console.error('Tracking error:', error);
      throw error;
    }
  }

  async getCustomerOrders(email, page = 1, limit = 10) {
    try {
      const url = new URL(`${this.baseUrl}/orders/customer/${email}`);
      url.searchParams.append('page', page.toString());
      url.searchParams.append('limit', limit.toString());

      const response = await fetch(url);
      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error.message);
      }

      return data.data;
    } catch (error) {
      console.error('Customer orders error:', error);
      throw error;
    }
  }

  async subscribeToNotifications(identifier, email, phone = null) {
    try {
      const response = await fetch(
        `${this.baseUrl}/orders/track/${identifier}/notifications`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            email,
            phone,
            notificationTypes: ['shipped', 'delivered', 'exception']
          })
        }
      );

      const data = await response.json();

      if (!data.success) {
        throw new Error(data.error.message);
      }

      return data.data;
    } catch (error) {
      console.error('Notification subscription error:', error);
      throw error;
    }
  }
}

// Usage Example
const tracker = new MyProfileTracker();

// Track an order
tracker.trackOrder('MP-2024-001234')
  .then(order => {
    console.log('Order status:', order.status);
    console.log('Tracking events:', order.trackingEvents);
  })
  .catch(error => {
    console.error('Failed to track order:', error.message);
  });
```

#### React Tracking Component
```jsx
import React, { useState, useEffect } from 'react';

const OrderTracker = ({ identifier, email }) => {
  const [order, setOrder] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (identifier) {
      trackOrder();
    }
  }, [identifier, email]);

  const trackOrder = async () => {
    setLoading(true);
    setError(null);

    try {
      const tracker = new MyProfileTracker();
      const orderData = await tracker.trackOrder(identifier, email);
      setOrder(orderData);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  if (loading) return <div>Loading...</div>;
  if (error) return <div className="error">Error: {error}</div>;
  if (!order) return <div>Enter an order number to track</div>;

  return (
    <div className="order-tracker">
      <h2>Order {order.orderNumber}</h2>
      <div className="status">
        Status: <span className={`status-${order.status}`}>{order.status}</span>
      </div>
      
      {order.trackingNumber && (
        <div className="tracking">
          Tracking: {order.trackingNumber} via {order.carrier}
        </div>
      )}

      <div className="timeline">
        <h3>Tracking History</h3>
        {order.trackingEvents.map((event, index) => (
          <div key={index} className="timeline-event">
            <div className="timestamp">
              {new Date(event.timestamp).toLocaleString()}
            </div>
            <div className="description">{event.description}</div>
            {event.location && (
              <div className="location">
                {event.location.city}, {event.location.state}
              </div>
            )}
          </div>
        ))}
      </div>
    </div>
  );
};

export default OrderTracker;
```

### WordPress Plugin Integration

#### Shortcode Implementation
```php
<?php
// Add to functions.php or plugin file

function myprofile_tracking_shortcode($atts) {
    $atts = shortcode_atts(array(
        'order' => '',
        'email' => '',
        'theme' => 'default'
    ), $atts);

    ob_start();
    ?>
    <div id="myprofile-tracker" 
         data-order="<?php echo esc_attr($atts['order']); ?>"
         data-email="<?php echo esc_attr($atts['email']); ?>"
         data-theme="<?php echo esc_attr($atts['theme']); ?>">
        <div class="tracker-loading">Loading order information...</div>
    </div>

    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const tracker = new MyProfileTracker();
        const container = document.getElementById('myprofile-tracker');
        const orderNumber = container.dataset.order;
        const email = container.dataset.email;

        if (orderNumber) {
            tracker.trackOrder(orderNumber, email)
                .then(order => {
                    container.innerHTML = renderOrderTracking(order);
                })
                .catch(error => {
                    container.innerHTML = `<div class="error">Error: ${error.message}</div>`;
                });
        }
    });

    function renderOrderTracking(order) {
        return `
            <div class="order-tracking">
                <h3>Order ${order.orderNumber}</h3>
                <p>Status: <span class="status-${order.status}">${order.status}</span></p>
                ${order.trackingNumber ? `<p>Tracking: ${order.trackingNumber}</p>` : ''}
                <div class="tracking-events">
                    ${order.trackingEvents.map(event => `
                        <div class="event">
                            <div class="time">${new Date(event.timestamp).toLocaleString()}</div>
                            <div class="desc">${event.description}</div>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;
    }
    </script>
    <?php
    return ob_get_clean();
}

add_shortcode('myprofile_tracking', 'myprofile_tracking_shortcode');

// Usage: [myprofile_tracking order="MP-2024-001234" email="<EMAIL>"]
?>
```

### Shopify Integration

#### Liquid Template
```liquid
<!-- Add to order-status.liquid -->
<div class="myprofile-tracking">
  <h2>Track Your Order</h2>
  
  {% if order.tracking_number %}
    <div class="tracking-info">
      <p><strong>Tracking Number:</strong> {{ order.tracking_number }}</p>
      <p><strong>Carrier:</strong> {{ order.tracking_company }}</p>
    </div>
    
    <div id="tracking-details" data-tracking="{{ order.tracking_number }}">
      Loading tracking information...
    </div>

    <script>
      fetch(`/api/orders/track/${order.tracking_number}`)
        .then(response => response.json())
        .then(data => {
          if (data.success) {
            document.getElementById('tracking-details').innerHTML = 
              renderTrackingDetails(data.data.order);
          }
        });

      function renderTrackingDetails(order) {
        return order.trackingEvents.map(event => `
          <div class="tracking-event">
            <div class="event-time">${new Date(event.timestamp).toLocaleString()}</div>
            <div class="event-desc">${event.description}</div>
            ${event.location ? `<div class="event-location">${event.location.city}, ${event.location.state}</div>` : ''}
          </div>
        `).join('');
      }
    </script>
  {% else %}
    <p>Tracking information will be available once your order ships.</p>
  {% endif %}
</div>
```

## Mobile Considerations

### Responsive Design Guidelines

#### Breakpoints
```css
/* Mobile First Approach */
.tracking-portal {
  padding: 1rem;
}

/* Tablet */
@media (min-width: 768px) {
  .tracking-portal {
    padding: 2rem;
    max-width: 800px;
    margin: 0 auto;
  }
}

/* Desktop */
@media (min-width: 1024px) {
  .tracking-portal {
    padding: 3rem;
    max-width: 1200px;
  }
}
```

#### Touch-Friendly Interface
```css
/* Minimum touch target size */
.tracking-button {
  min-height: 44px;
  min-width: 44px;
  padding: 12px 24px;
  font-size: 16px; /* Prevents zoom on iOS */
}

/* Spacing between clickable elements */
.tracking-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}
```

### Progressive Web App Features

#### Service Worker for Offline Tracking
```javascript
// sw.js
const CACHE_NAME = 'myprofile-tracking-v1';
const urlsToCache = [
  '/track',
  '/styles/tracking.css',
  '/scripts/tracker.js'
];

self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => cache.addAll(urlsToCache))
  );
});

self.addEventListener('fetch', event => {
  event.respondWith(
    caches.match(event.request)
      .then(response => {
        if (response) {
          return response;
        }
        return fetch(event.request);
      }
    )
  );
});
```

#### Web App Manifest
```json
{
  "name": "My Profile Order Tracking",
  "short_name": "MP Tracking",
  "description": "Track your My Profile orders on the go",
  "start_url": "/track",
  "display": "standalone",
  "background_color": "#ffffff",
  "theme_color": "#007bff",
  "icons": [
    {
      "src": "/icons/icon-192x192.png",
      "sizes": "192x192",
      "type": "image/png"
    },
    {
      "src": "/icons/icon-512x512.png",
      "sizes": "512x512",
      "type": "image/png"
    }
  ]
}
```

## Support & Help

### Customer Self-Service

#### FAQ Integration
Common questions customers can resolve themselves:

1. **"Where's my order?"**
   - Check tracking portal with order number
   - Look for shipping email with tracking number
   - Account for processing time (1-2 business days)

2. **"Why is my package delayed?"**
   - Check tracking events for carrier delays
   - Weather/holiday impacts on delivery
   - Address validation issues

3. **"I can't find my order number"**
   - Check order confirmation email
   - Search email for "My Profile" or "MP-2024"
   - Contact support with payment method details

4. **"My package says delivered but I don't see it"**
   - Check all entrances and delivery areas
   - Ask neighbors or building management
   - Check for delivery photo/signature details
   - Wait 24 hours for delayed scan updates

#### Help Widget Integration
```javascript
// Customer support widget
class HelpWidget {
  constructor() {
    this.init();
  }

  init() {
    const widget = document.createElement('div');
    widget.innerHTML = `
      <div class="help-widget">
        <button class="help-trigger">Need Help?</button>
        <div class="help-menu" style="display: none;">
          <a href="/faq">Shipping FAQ</a>
          <a href="mailto:<EMAIL>">Email Support</a>
          <a href="tel:******-MY-PROFILE">Call Support</a>
          <button onclick="this.startChat()">Live Chat</button>
        </div>
      </div>
    `;
    
    document.body.appendChild(widget);
    this.bindEvents();
  }

  startChat() {
    // Integration with support chat system
    window.Intercom && window.Intercom('show');
  }
}

new HelpWidget();
```

### Contact Information

#### Support Channels
- **Email**: <EMAIL>
- **Phone**: 1-555-MY-PROFILE (**************)
- **Hours**: Monday-Friday 9AM-6PM PST
- **Live Chat**: Available on tracking portal
- **Social Media**: @MyProfileOfficial

#### Escalation Process
1. **Level 1**: General shipping questions via email/chat
2. **Level 2**: Delivery issues requiring carrier contact
3. **Level 3**: Lost/damaged packages requiring claims
4. **Priority**: High-value orders ($500+) get priority support

### Integration Support

#### Developer Resources
- **API Documentation**: https://docs.getmyprofile.online/api
- **SDK Downloads**: https://github.com/myprofile/shipping-sdk
- **Webhook Testing**: https://webhook.site integration
- **Postman Collection**: Available for API testing

#### Technical Support
- **Email**: <EMAIL>
- **Documentation**: https://docs.getmyprofile.online
- **Discord**: https://discord.gg/myprofile-dev
- **Office Hours**: Tuesdays 2-4PM PST via Zoom

### Feedback & Improvements

#### Customer Feedback Collection
```javascript
// Post-delivery survey integration
function showDeliveryFeedback(orderNumber) {
  const modal = document.createElement('div');
  modal.innerHTML = `
    <div class="feedback-modal">
      <h3>How was your delivery experience?</h3>
      <div class="rating-stars" data-order="${orderNumber}">
        ${[1,2,3,4,5].map(n => `<span class="star" data-rating="${n}">⭐</span>`).join('')}
      </div>
      <textarea placeholder="Tell us about your experience..."></textarea>
      <button onclick="submitFeedback('${orderNumber}')">Submit Feedback</button>
    </div>
  `;
  document.body.appendChild(modal);
}

function submitFeedback(orderNumber) {
  // Send feedback to analytics system
  fetch('/api/feedback/delivery', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      orderNumber,
      rating: selectedRating,
      comments: feedbackText
    })
  });
}
```

This comprehensive client documentation provides customers and developers with everything needed to effectively use the My Profile Order & Shipping System's customer-facing features.