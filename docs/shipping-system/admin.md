# My Profile Order & Shipping System - Admin Documentation

## Overview

The My Profile Order & Shipping System provides comprehensive order management, fulfillment, and shipping capabilities integrated with major US carriers (USPS, UPS, FedEx, DHL). This admin documentation covers all administrative features and capabilities.

## Table of Contents

1. [System Architecture](#system-architecture)
2. [Admin Interfaces](#admin-interfaces)
3. [Order Management](#order-management)
4. [Shipping Configuration](#shipping-configuration)
5. [Carrier Integration](#carrier-integration)
6. [Webhook Management](#webhook-management)
7. [Analytics & Reporting](#analytics--reporting)
8. [API Reference](#api-reference)
9. [Troubleshooting](#troubleshooting)

## System Architecture

### Core Components

- **EnhancedOrder Model**: Extended order schema with shipping-specific fields
- **ShippingService**: Main orchestrator for carrier operations
- **FulfillmentService**: Handles complete order fulfillment workflow
- **WebhookService**: Manages carrier webhook subscriptions and processing
- **Carrier Services**: Individual carrier integrations (USPS, UPS, FedEx, DHL)

### Database Schema

#### EnhancedOrder Document
```typescript
{
  orderNumber: string;           // Format: MP-YYYY-XXXXXX
  customer: {
    email: string;
    fullName: string;
    userId?: ObjectId;
  };
  items: [{
    product: ObjectId;           // Reference to Product
    productName: string;
    quantity: number;
    unitPrice: number;
    sku?: string;
  }];
  pricing: {
    subtotal: number;
    shippingCost: number;
    tax: number;
    totalAmount: number;
  };
  shipping: {
    shippingTier: 'lightweight' | 'standard' | 'premium' | 'bundle';
    packageConfiguration: 'envelope' | 'bubble_mailer' | 'rigid_mailer' | 'small_box' | 'medium_box';
    carrier?: string;
    trackingNumber?: string;
    labelUrl?: string;
    estimatedDelivery?: Date;
    address: ShippingAddress;
    trackingEvents: TrackingEvent[];
  };
  status: string;
  placedAt: Date;
  shippedAt?: Date;
  deliveredAt?: Date;
}
```

### Shipping Tier Logic

| Tier | Criteria | Package Type | Carrier Priority |
|------|----------|--------------|------------------|
| Lightweight | Total < $25 | envelope, bubble_mailer | USPS First |
| Standard | $25 - $99 | rigid_mailer, small_box | UPS/USPS |
| Premium | $100 - $249 | small_box, medium_box | FedEx/UPS |
| Bundle | $250+ or >3 items | medium_box, large_box | FedEx Express |

### Product Categorization

- **Cards** ($349.99): Premium tier, rigid_mailer
- **Bands** ($99.99): Standard tier, small_box
- **Stickers** ($99.99): Lightweight tier, envelope
- **Key Tags** ($10.75): Lightweight tier, envelope
- **Accessories**: Variable tier based on total

## Admin Interfaces

### Order Dashboard (`/admin/orders`)

**Location**: `src/views/admin/orders/OrderDashboard.tsx`

#### Features
- Real-time order overview with key metrics
- Advanced filtering and search capabilities
- Bulk order operations
- One-click fulfillment with optimal rate selection
- Detailed order inspection with tracking timeline

#### Key Metrics Displayed
- Total Orders
- Pending Orders (awaiting fulfillment)
- Shipped Orders (in transit)
- Fulfillment Rate (percentage delivered on time)

#### Filter Options
- Status: pending, confirmed, processing, shipped, delivered, cancelled
- Date range selection
- Customer search (name, email, order number)
- Order value ranges

#### Actions Available
- **Fulfill Order**: Automatically selects optimal carrier rate and generates shipping label
- **View Details**: Complete order information with customer and shipping details
- **Track Package**: Real-time tracking information
- **Resend Notifications**: Customer email/SMS notifications

### Shipping Dashboard (`/admin/shipping`)

**Location**: `src/views/admin/shipping/ShippingDashboard.tsx`

#### Features
- Carrier health monitoring
- Webhook status verification
- Testing tools for tracking and webhooks
- Rate comparison utilities

#### Tabs

1. **Carrier Health**
   - Real-time status of all carriers
   - Response time monitoring
   - Usage distribution charts
   - Service availability alerts

2. **Webhook Status**
   - Webhook endpoint configuration
   - Signature verification status
   - Event processing logs
   - Server time synchronization

3. **Testing Tools**
   - Package tracking tester
   - Webhook event simulator
   - Rate calculation testing

### Analytics Dashboard (`/admin/analytics`)

**Location**: `src/views/admin/analytics/OrderAnalytics.tsx`

#### Key Performance Indicators
- Average Fulfillment Time (target: <24 hours)
- On-Time Delivery Rate (target: >95%)
- Cost Per Shipment
- Customer Satisfaction Rating

#### Charts & Visualizations
- Order trends and revenue over time
- Shipping tier distribution (pie chart)
- Carrier performance comparison
- Delivery time analysis

#### Insights Generated
- Carrier performance recommendations
- Package optimization opportunities
- Cost reduction suggestions
- Fulfillment workflow improvements

## Order Management

### Order Lifecycle

1. **Order Placed** → `pending`
2. **Payment Confirmed** → `confirmed`
3. **Inventory Reserved** → `processing`
4. **Label Generated** → `shipped`
5. **In Transit** → tracking updates
6. **Delivered** → `delivered`

### Fulfillment Process

#### Automatic Fulfillment
```typescript
POST /api/orders/:orderId/fulfill
{
  "autoSelectOptimalRate": true,
  "generateLabelImmediately": true,
  "sendNotifications": true
}
```

#### Manual Rate Selection
```typescript
POST /api/orders/:orderId/fulfill
{
  "carrierRateId": "ups_ground_rate_123",
  "generateLabelImmediately": true,
  "sendNotifications": true
}
```

#### Fulfillment Options
- **autoSelectOptimalRate**: Uses cost optimization algorithm
- **generateLabelImmediately**: Creates shipping label upon fulfillment
- **sendNotifications**: Triggers customer email/SMS notifications
- **requireSignature**: For high-value orders (>$200)
- **insurance**: Automatic for orders >$100

### Batch Operations

#### Bulk Fulfillment
```bash
POST /api/orders/bulk-fulfill
{
  "orderIds": ["order1", "order2", "order3"],
  "options": {
    "autoSelectOptimalRate": true,
    "generateLabelImmediately": true
  }
}
```

#### Status Updates
```bash
POST /api/orders/bulk-update
{
  "orderIds": ["order1", "order2"],
  "status": "processing",
  "reason": "Inventory confirmed"
}
```

## Shipping Configuration

### Carrier Setup

#### USPS Configuration
```typescript
// Environment Variables Required
USPS_CLIENT_ID=your_client_id
USPS_CLIENT_SECRET=your_client_secret
USPS_BASE_URL=https://api.usps.com
USPS_WEBHOOK_SECRET=your_webhook_secret
```

#### UPS Configuration
```typescript
UPS_ACCESS_LICENSE_NUMBER=your_license
UPS_USERNAME=your_username
UPS_PASSWORD=your_password
UPS_BASE_URL=https://onlinetools.ups.com
UPS_WEBHOOK_SECRET=your_webhook_secret
```

#### FedEx Configuration
```typescript
FEDEX_ACCOUNT_NUMBER=your_account
FEDEX_METER_NUMBER=your_meter
FEDEX_KEY=your_key
FEDEX_PASSWORD=your_password
FEDEX_BASE_URL=https://ws.fedex.com
FEDEX_WEBHOOK_SECRET=your_webhook_secret
```

#### DHL Configuration
```typescript
DHL_SITE_ID=your_site_id
DHL_PASSWORD=your_password
DHL_BASE_URL=https://xmlpi-ea.dhl.com
DHL_WEBHOOK_SECRET=your_webhook_secret
```

### Rate Shopping Algorithm

The system automatically selects the optimal carrier based on:

1. **Cost Optimization** (40% weight)
2. **Delivery Speed** (25% weight)
3. **Reliability Score** (20% weight)
4. **Shipping Tier Requirements** (15% weight)

#### Algorithm Implementation
```typescript
class ShippingService {
  async getOptimalRate(order: IEnhancedOrder): Promise<RateOption | null> {
    const rates = await this.getRatesForOrder(order);
    
    return rates
      .map(rate => ({
        ...rate,
        score: this.calculateScore(rate, order)
      }))
      .sort((a, b) => b.score - a.score)[0];
  }
  
  private calculateScore(rate: RateOption, order: IEnhancedOrder): number {
    const costScore = 1 - (rate.cost / this.maxCost) * 0.4;
    const speedScore = (1 - rate.transitDays / 7) * 0.25;
    const reliabilityScore = rate.reliability * 0.2;
    const tierScore = this.getTierScore(rate, order) * 0.15;
    
    return costScore + speedScore + reliabilityScore + tierScore;
  }
}
```

### Package Optimization

#### Automated Package Selection
```typescript
private determinePackageConfiguration(order: IEnhancedOrder): PackageType {
  const totalValue = order.pricing.totalAmount;
  const itemCount = order.items.reduce((sum, item) => sum + item.quantity, 0);
  const hasFragileItems = order.items.some(item => 
    item.productName.toLowerCase().includes('card')
  );
  
  if (hasFragileItems || totalValue > 200) {
    return 'rigid_mailer';
  }
  
  if (totalValue < 25 && itemCount <= 2) {
    return 'envelope';
  }
  
  if (totalValue < 100) {
    return 'bubble_mailer';
  }
  
  return itemCount > 5 ? 'medium_box' : 'small_box';
}
```

## Carrier Integration

### USPS Integration

#### OAuth 2.0 Authentication
```typescript
class USPSService extends BaseCarrier {
  private async authenticate(): Promise<string> {
    const response = await fetch(`${this.baseUrl}/oauth2/v3/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Basic ${this.getBasicAuth()}`
      },
      body: JSON.stringify({
        grant_type: 'client_credentials',
        scope: 'addresses prices labels tracking'
      })
    });
    
    const data = await response.json();
    return data.access_token;
  }
}
```

#### Rate Calculation
```typescript
async getRates(shipment: ShipmentRequest): Promise<RateOption[]> {
  const token = await this.ensureValidToken();
  
  const response = await fetch(`${this.baseUrl}/prices/v3/base-rates/search`, {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      originZIPCode: shipment.origin.zipCode,
      destinationZIPCode: shipment.destination.zipCode,
      weight: shipment.package.weight,
      length: shipment.package.dimensions.length,
      width: shipment.package.dimensions.width,
      height: shipment.package.dimensions.height,
      priceType: 'RETAIL',
      processingCategory: 'MACHINABLE'
    })
  });
  
  return this.parseRateResponse(await response.json());
}
```

### UPS Integration

#### Address Validation
```typescript
async validateAddress(address: Address): Promise<AddressValidationResult> {
  const response = await fetch(`${this.baseUrl}/addressvalidation/v1/1`, {
    method: 'POST',
    headers: this.getHeaders(),
    body: JSON.stringify({
      UPSSecurity: this.getSecurityCredentials(),
      AddressValidationRequest: {
        Request: { RequestOption: '1' },
        AddressKeyFormat: {
          AddressLine: address.street,
          PoliticalDivision2: address.city,
          PoliticalDivision1: address.state,
          PostcodePrimaryLow: address.zipCode,
          CountryCode: address.country
        }
      }
    })
  });
  
  return this.parseAddressValidationResponse(await response.json());
}
```

### FedEx Integration

#### Shipment Tracking
```typescript
async trackShipment(trackingNumber: string): Promise<TrackingInfo> {
  const response = await fetch(`${this.baseUrl}/track/v1/trackingnumbers`, {
    method: 'POST',
    headers: this.getHeaders(),
    body: JSON.stringify({
      trackingInfo: [
        {
          trackingNumberInfo: {
            trackingNumber: trackingNumber
          }
        }
      ],
      includeDetailedScans: true
    })
  });
  
  return this.parseTrackingResponse(await response.json());
}
```

### DHL Integration

#### International Capabilities
```typescript
async getInternationalRates(shipment: ShipmentRequest): Promise<RateOption[]> {
  const response = await fetch(`${this.baseUrl}/XMLShippingAPI`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/xml' },
    body: this.buildDHLRateRequest(shipment)
  });
  
  return this.parseDHLRateResponse(await response.text());
}
```

## Webhook Management

### Webhook Setup

#### Automatic Registration
```bash
# Run setup script
bun run src/scripts/setup-webhooks.ts setup
```

#### Manual Registration
```typescript
import { webhookService } from '../services/WebhookService';

await webhookService.registerWebhooks();
```

### Webhook Endpoints

#### USPS Webhook
```
POST /api/webhooks/shipping/usps
Content-Type: application/json
X-USPS-Signature: sha256=...
```

#### UPS Webhook
```
POST /api/webhooks/shipping/ups
Content-Type: application/json
X-UPS-Signature: sha256=...
```

#### FedEx Webhook
```
POST /api/webhooks/shipping/fedex
Content-Type: application/json
X-FedEx-Signature: sha256=...
```

#### DHL Webhook
```
POST /api/webhooks/shipping/dhl
Content-Type: application/json
X-DHL-Signature: sha256=...
```

### Event Processing

#### Tracking Event Types
- `in_transit`: Package picked up by carrier
- `out_for_delivery`: Package out for delivery
- `delivered`: Package delivered successfully
- `exception`: Delivery exception occurred
- `returned`: Package returned to sender

#### Event Handler
```typescript
export const processTrackingEvent = async (
  trackingNumber: string,
  event: TrackingEventData,
  carrier: string
): Promise<void> => {
  const order = await EnhancedOrder.findOne({
    'shipping.trackingNumber': trackingNumber
  });
  
  if (!order) return;
  
  // Add tracking event
  order.shipping.trackingEvents.push({
    timestamp: new Date(event.timestamp),
    status: event.status,
    description: event.description,
    location: event.location,
    carrier: carrier
  });
  
  // Update order status
  if (event.status === 'delivered') {
    order.status = 'delivered';
    order.deliveredAt = new Date(event.timestamp);
  }
  
  await order.save();
  
  // Send customer notification
  await notificationService.sendTrackingUpdate(order, event);
};
```

### Signature Verification

#### USPS Signature Verification
```typescript
function verifyUSPSSignature(
  payload: string,
  signature: string,
  secret: string
): boolean {
  const hmac = crypto.createHmac('sha256', secret);
  hmac.update(payload);
  const digest = hmac.digest('hex');
  
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(digest, 'hex')
  );
}
```

## Analytics & Reporting

### Real-time Metrics

#### Order Analytics API
```
GET /api/orders/analytics?period=30d
```

Response:
```json
{
  "success": true,
  "data": {
    "analytics": {
      "totalOrders": 1247,
      "pendingOrders": 23,
      "shippedOrders": 156,
      "deliveredOrders": 1068,
      "fulfillmentRate": "94.2",
      "averageOrderValue": 87.65,
      "averageFulfillmentTime": 18.5,
      "onTimeDeliveryRate": 94.2,
      "costPerShipment": 11.85
    }
  }
}
```

#### Shipping Metrics API
```
GET /api/orders/metrics
```

Response:
```json
{
  "success": true,
  "data": {
    "metrics": {
      "totalShipments": 1224,
      "inTransit": 156,
      "delivered": 1068,
      "exceptions": 12,
      "carrierBreakdown": {
        "USPS": 623,
        "UPS": 367,
        "FedEx": 189,
        "DHL": 45
      },
      "averageDeliveryTime": 2.8
    }
  }
}
```

### Performance Monitoring

#### Carrier Performance Tracking
```typescript
interface CarrierPerformance {
  carrier: string;
  totalShipments: number;
  averageDeliveryTime: number;
  onTimeRate: number;
  exceptionRate: number;
  averageCost: number;
  customerSatisfaction: number;
}
```

#### Service Level Monitoring
- Target fulfillment time: <24 hours
- Target delivery accuracy: >95%
- Target cost efficiency: <$12 per shipment
- Target customer satisfaction: >4.5/5

### Reporting Capabilities

#### Available Reports
1. **Daily Order Summary**: Orders placed, fulfilled, and delivered
2. **Carrier Performance Report**: Comparative analysis of all carriers
3. **Cost Analysis Report**: Shipping costs vs. revenue analysis
4. **Customer Satisfaction Report**: Delivery feedback and ratings
5. **Exception Report**: Failed deliveries and their causes

#### Export Formats
- CSV for data analysis
- PDF for executive reports
- JSON for API integration
- Excel for advanced filtering

## API Reference

### Admin Shipping Management

#### Dashboard & Overview

**Get Shipping Dashboard**
```http
GET /api/admin/shipping/dashboard
Query Parameters:
- period: string (default: "30d") - Time period for metrics

Response:
{
  "success": true,
  "data": {
    "stats": {
      "totalShipments": 1247,
      "activeCarriers": 4,
      "pendingOrders": 23,
      "avgDeliveryTime": 2.8,
      "onTimeRate": 94.2,
      "totalShippingCost": 15240.50
    },
    "carrierStats": [...],
    "recentActivity": [...],
    "period": "30d"
  }
}
```

**Get System Health**
```http
GET /api/admin/shipping/health

Response:
{
  "success": true,
  "data": {
    "systemHealth": "healthy",
    "database": "connected",
    "carriers": {
      "USPS": { "status": "active", "health": 98 },
      "UPS": { "status": "active", "health": 96 }
    },
    "webhooks": {
      "status": "operational",
      "processedToday": 1247,
      "failedToday": 3
    }
  }
}
```

#### Carrier Management

**Get All Carriers**
```http
GET /api/admin/shipping/carriers

Response:
{
  "success": true,
  "data": {
    "carriers": [
      {
        "id": "usps-001",
        "name": "USPS",
        "status": "active",
        "health": 98,
        "apiEndpoint": "https://api.usps.com",
        "authType": "oauth2",
        "services": ["First Class", "Priority", "Express"],
        "monthlyUsage": 623,
        "monthlyLimit": 5000,
        "costPerShipment": 8.45,
        "lastSync": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

**Update Carrier Configuration**
```http
PUT /api/admin/shipping/carriers/:carrierId
Content-Type: application/json

Body:
{
  "apiEndpoint": "https://api.usps.com",
  "authType": "oauth2",
  "credentials": {
    "clientId": "your_client_id",
    "clientSecret": "your_client_secret"
  },
  "services": ["First Class", "Priority", "Express"],
  "rateLimit": 1000,
  "timeout": 30,
  "retryAttempts": 3
}

Response:
{
  "success": true,
  "message": "Carrier configuration updated successfully",
  "data": { "carrierId": "usps-001", ... }
}
```

**Test Carrier Connection**
```http
POST /api/admin/shipping/carriers/:carrierId/test
Content-Type: application/json

Body:
{
  "testType": "connection" // or "rates" or "tracking"
}

Response:
{
  "success": true,
  "message": "connection test completed successfully",
  "data": {
    "carrierId": "usps-001",
    "testType": "connection",
    "responseTime": 245,
    "timestamp": "2024-01-15T10:30:00Z"
  }
}
```

#### Rules & Tiers Management

**Get Shipping Rules and Tiers**
```http
GET /api/admin/shipping/rules

Response:
{
  "success": true,
  "data": {
    "rules": [
      {
        "id": "free-shipping-cards",
        "name": "Free Shipping for Profile Cards",
        "description": "Free shipping for orders containing Profile Cards over $300",
        "type": "price",
        "conditions": [
          { "field": "orderValue", "operator": ">=", "value": 300 },
          { "field": "productCategory", "operator": "contains", "value": "cards" }
        ],
        "actions": [
          { "type": "setShippingCost", "value": 0 }
        ],
        "isActive": true,
        "priority": 1
      }
    ],
    "tiers": [
      {
        "id": "lightweight",
        "name": "Lightweight",
        "minValue": 0,
        "maxValue": 25,
        "baseCost": 4.95,
        "carriers": ["USPS"],
        "isActive": true
      }
    ]
  }
}
```

**Create Shipping Rule**
```http
POST /api/admin/shipping/rules
Content-Type: application/json

Body:
{
  "name": "Bulk Order Discount",
  "description": "50% shipping discount for orders with 5+ items",
  "type": "custom",
  "conditions": [
    { "field": "itemCount", "operator": ">=", "value": 5 }
  ],
  "actions": [
    { "type": "multiplyShippingCost", "value": 0.5 }
  ],
  "isActive": true,
  "priority": 2
}

Response:
{
  "success": true,
  "message": "Shipping rule created successfully",
  "data": {
    "id": "rule_1705401234567",
    "name": "Bulk Order Discount",
    ...
  }
}
```

#### Analytics & Reporting

**Get Shipping Analytics**
```http
GET /api/admin/shipping/analytics
Query Parameters:
- period: string (default: "30d")
- metric: string (optional) - specific metric to fetch

Response:
{
  "success": true,
  "data": {
    "shipmentTrends": [
      {
        "date": "2024-01-15",
        "carrier": "USPS",
        "shipments": 45,
        "cost": 520,
        "avgDeliveryTime": 2.8
      }
    ],
    "carrierPerformance": [
      {
        "carrier": "USPS",
        "totalShipments": 623,
        "avgCost": 8.45,
        "onTimeRate": 95.1
      }
    ]
  }
}
```

**Generate Analytics Report**
```http
POST /api/admin/shipping/analytics
Content-Type: application/json

Body:
{
  "action": "generate_report",
  "filters": { "carrier": "USPS" },
  "dateRange": "30d"
}

Response:
{
  "success": true,
  "message": "Analytics report generated successfully",
  "data": {
    "reportId": "report_1705401234567",
    "filename": "shipping_analytics_2024-01-15.pdf",
    "downloadUrl": "/api/admin/shipping/analytics/download/report_1705401234567",
    "generatedAt": "2024-01-15T10:30:00Z"
  }
}
```

#### Templates Management

**Get Shipping Templates**
```http
GET /api/admin/shipping/templates

Response:
{
  "success": true,
  "data": {
    "templates": [
      {
        "id": "tpl-cards-premium",
        "name": "Profile Cards Premium",
        "description": "Premium shipping template for high-value Profile Cards",
        "category": "product",
        "config": {
          "shippingTier": "premium",
          "packageType": "rigid_mailer",
          "carriers": ["FedEx", "UPS"]
        },
        "pricing": {
          "baseCost": 14.95
        },
        "isActive": true,
        "usageCount": 342
      }
    ]
  }
}
```

#### Operations

**Bulk Sync Carrier Rates**
```http
POST /api/admin/shipping/sync-rates
Content-Type: application/json

Body:
{
  "carriers": ["usps", "ups", "fedex", "dhl"] // optional, defaults to all
}

Response:
{
  "success": true,
  "message": "Carrier rates synchronized successfully",
  "data": {
    "syncResults": [
      {
        "carrier": "usps",
        "success": true,
        "ratesUpdated": 45,
        "timestamp": "2024-01-15T10:30:00Z"
      }
    ]
  }
}
```

**Export Shipping Data**
```http
GET /api/admin/shipping/export
Query Parameters:
- type: string (default: "orders") - Data type to export
- format: string (default: "csv") - Export format
- period: string (default: "30d") - Time period

Response:
{
  "success": true,
  "message": "Export generated successfully",
  "data": {
    "type": "orders",
    "format": "csv",
    "period": "30d",
    "recordCount": 1247,
    "downloadUrl": "https://api.myprofile.com/exports/shipping-orders-1705401234567.csv",
    "expiresAt": "2024-01-16T10:30:00Z"
  }
}
```

### Admin Order Management

#### Get All Orders
```
GET /api/orders
Query Parameters:
- page: number (default: 1)
- limit: number (default: 50)
- status: string (optional)
- search: string (optional)
- startDate: string (optional)
- endDate: string (optional)
```

#### Get Order Details
```
GET /api/orders/:orderId
```

#### Fulfill Order
```
POST /api/orders/:orderId/fulfill
Body:
{
  "autoSelectOptimalRate": boolean,
  "carrierRateId": string (optional),
  "generateLabelImmediately": boolean,
  "sendNotifications": boolean,
  "requireSignature": boolean (optional),
  "insurance": boolean (optional)
}
```

#### Update Order Status
```
PUT /api/orders/:orderId/status
Body:
{
  "status": string,
  "reason": string (optional)
}
```

#### Cancel Order
```
DELETE /api/orders/:orderId
Body:
{
  "reason": string,
  "refundAmount": number (optional)
}
```

### Shipping Management

#### Get Available Rates
```
GET /api/shipping/rates/:orderId
```

#### Test Carrier Connection
```
POST /api/shipping/test/:carrier
Body:
{
  "testType": "connection" | "rates" | "tracking"
}
```

#### Get Carrier Health Status
```
GET /api/shipping/health
```

#### Refresh Tracking Data
```
POST /api/shipping/track/:trackingNumber/refresh
```

### Webhook Management

#### Get Webhook Status
```
GET /api/webhooks/shipping/status
```

#### Test Webhook Processing
```
POST /api/webhooks/shipping/test
Body:
{
  "trackingNumber": string,
  "carrier": string,
  "status": string,
  "location": object,
  "eventDetails": string
}
```

#### Register Webhooks
```
POST /api/webhooks/shipping/register
Body:
{
  "carriers": string[],
  "events": string[]
}
```

#### Unsubscribe Webhooks
```
DELETE /api/webhooks/shipping/unsubscribe
Body:
{
  "carriers": string[]
}
```

## Troubleshooting

### Common Issues

#### 1. Webhook Not Receiving Events
**Symptoms**: Orders not updating with tracking information
**Causes**:
- Webhook URL not accessible
- Signature verification failing
- Carrier subscription not active

**Solutions**:
```bash
# Check webhook status
curl -X GET /api/webhooks/shipping/status

# Test webhook processing
curl -X POST /api/webhooks/shipping/test

# Re-register webhooks
bun run src/scripts/setup-webhooks.ts setup
```

#### 2. Rate Calculation Failing
**Symptoms**: Orders stuck in "confirmed" status
**Causes**:
- Invalid shipping address
- Carrier API unavailable
- Package dimensions not set

**Solutions**:
```bash
# Test carrier connectivity
curl -X POST /api/shipping/test/usps

# Validate address
curl -X POST /api/shipping/validate-address

# Check system logs
tail -f logs/shipping.log
```

#### 3. Label Generation Errors
**Symptoms**: Orders fulfilled but no tracking number
**Causes**:
- Insufficient carrier account balance
- Invalid package configuration
- API rate limiting

**Solutions**:
```bash
# Check carrier account status
curl -X GET /api/shipping/health

# Retry label generation
curl -X POST /api/orders/:orderId/retry-label

# Review error logs
grep "label generation" logs/error.log
```

#### 4. High Shipping Costs
**Symptoms**: Shipping costs higher than expected
**Causes**:
- Incorrect package dimensions
- Wrong shipping tier assignment
- Rate shopping disabled

**Solutions**:
1. Review package configuration logic
2. Verify dimensional weight calculations
3. Enable rate shopping for all carriers
4. Adjust shipping tier thresholds

### Monitoring Commands

#### Check System Health
```bash
# Overall system status
curl -X GET /api/health

# Carrier-specific health
curl -X GET /api/shipping/health

# Webhook status
curl -X GET /api/webhooks/shipping/status
```

#### Performance Monitoring
```bash
# View recent orders
curl -X GET "/api/orders?limit=10&sort=createdAt"

# Check fulfillment rate
curl -X GET "/api/orders/analytics?metric=fulfillment"

# Monitor tracking events
curl -X GET "/api/webhooks/shipping/events?limit=50"
```

### Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| SHIP_001 | Invalid shipping address | Validate address format |
| SHIP_002 | Carrier API unavailable | Check carrier status |
| SHIP_003 | Rate calculation failed | Verify package dimensions |
| SHIP_004 | Label generation failed | Check carrier account |
| SHIP_005 | Webhook verification failed | Verify signature secret |
| SHIP_006 | Tracking number not found | Wait for carrier update |
| SHIP_007 | Package configuration invalid | Check product mapping |
| SHIP_008 | Delivery exception | Contact carrier support |

## Frontend Integration

### Admin Panel API Client

The admin frontend uses a TypeScript API client to consume all shipping endpoints:

**Location**: `my-profile-admin/src/lib/api/shipping-api.ts`

#### Usage Examples

```typescript
import { ShippingAPI } from '@/lib/api/shipping-api';

// Get dashboard data
const dashboardData = await ShippingAPI.getDashboard('30d');

// Get all carriers
const carriers = await ShippingAPI.getCarriers();

// Test carrier connection
const testResult = await ShippingAPI.testCarrier('usps-001', 'connection');

// Update carrier configuration
await ShippingAPI.updateCarrier('usps-001', {
  apiEndpoint: 'https://api.usps.com',
  rateLimit: 1000
});

// Get analytics
const analytics = await ShippingAPI.getAnalytics('30d', 'onTimeRate');

// Sync rates
const syncResults = await ShippingAPI.syncRates(['usps', 'ups']);

// Generate report
const report = await ShippingAPI.generateReport(
  { carrier: 'USPS' }, 
  '30d'
);
```

#### API Client Architecture

```typescript
// Frontend (my-profile-admin)
┌─────────────────────────┐
│   React Components      │ ← Admin UI components
├─────────────────────────┤
│   ShippingAPI Client    │ ← TypeScript API client
├─────────────────────────┤
│   HTTP Requests         │ ← Axios/Fetch calls
└─────────────────────────┘
           │
           │ HTTP/JSON
           ▼
┌─────────────────────────┐
│  Backend API Endpoints  │ ← Express.js routes
├─────────────────────────┤
│  Controller Logic       │ ← Business logic
├─────────────────────────┤
│  Service Layer          │ ← Shipping services
├─────────────────────────┤
│  Database Layer         │ ← MongoDB collections
└─────────────────────────┘
```

#### Error Handling

The API client includes comprehensive error handling:

```typescript
try {
  const carriers = await ShippingAPI.getCarriers();
  // Handle success
} catch (error) {
  if (error.response?.status === 401) {
    // Handle authentication error
    redirectToLogin();
  } else if (error.response?.status === 403) {
    // Handle authorization error
    showAccessDeniedMessage();
  } else {
    // Handle general error
    showErrorToast(error.message);
  }
}
```

#### Type Safety

All API responses are fully typed:

```typescript
interface ShippingDashboardData {
  stats: {
    totalShipments: number;
    activeCarriers: number;
    pendingOrders: number;
    avgDeliveryTime: number;
    onTimeRate: number;
    totalShippingCost: number;
  };
  carrierStats: Carrier[];
  recentActivity: ActivityEvent[];
  period: string;
}
```

### Component Integration

#### Using the API in React Components

```typescript
import { useState, useEffect } from 'react';
import { ShippingAPI, type ShippingDashboardData } from '@/lib/api/shipping-api';

const ShippingDashboard = () => {
  const [data, setData] = useState<ShippingDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDashboard = async () => {
      try {
        const dashboardData = await ShippingAPI.getDashboard('30d');
        setData(dashboardData);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setLoading(false);
      }
    };

    fetchDashboard();
  }, []);

  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  if (!data) return <NoDataMessage />;

  return (
    <div>
      <h1>Shipping Dashboard</h1>
      <StatCard 
        title="Total Shipments" 
        value={data.stats.totalShipments} 
      />
      <StatCard 
        title="On-Time Rate" 
        value={`${data.stats.onTimeRate}%`} 
      />
      {/* More dashboard components */}
    </div>
  );
};
```

#### Real-time Data Updates

```typescript
// Auto-refresh dashboard data every 30 seconds
useEffect(() => {
  const interval = setInterval(async () => {
    try {
      const freshData = await ShippingAPI.getDashboard('30d');
      setData(freshData);
    } catch (error) {
      console.error('Failed to refresh dashboard:', error);
    }
  }, 30000);

  return () => clearInterval(interval);
}, []);
```

### Contact Support

For technical issues:
- **Email**: <EMAIL>
- **Slack**: #shipping-system
- **Documentation**: [Internal Wiki](https://wiki.getmyprofile.online/shipping)
- **On-call**: Use PagerDuty for critical issues

For carrier-specific issues:
- **USPS**: 1-800-ASK-USPS
- **UPS**: 1-800-PICK-UPS
- **FedEx**: 1-800-GO-FEDEX
- **DHL**: 1-800-CALL-DHL