# MyProfile Product API Documentation

This covers the product endpoints that frontend apps actually use - no admin stuff, just the endpoints our users hit when browsing products, activating them, and managing QR codes.

## Base URLs

- **Production**: `https://app.getmyprofile.online/api`
- **Local Development**: `http://localhost:3000/api`

## Authentication

**Public Endpoints (No Auth Required):**
- Product browsing (`GET /product-pools`, `/categories`, `/materials`, `/product-variants`)
- Product search and details
- QR code lookup (for activation)

**Protected Endpoints (JWT Required):**
- Product allocation/purchase
- User's product management
- QR code analytics



For protected endpoints only, send JWT tokens like this:

### Headers
```http
Authorization: Bearer <jwt_token>
X-Profile-Id: <profile_id>  # Optional: specify which profile to use
```

### Cookies
```http
Cookie: accessToken=<jwt_token>; profileToken=<profile_token>
```

### Profile Authentication
Some endpoints need to know which profile you're working with. Either stick the profile ID in the `X-Profile-Id` header
## Response Format

All responses look like this:

### Success Response
```json
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "error": "Detailed error information"
}
```



## Product Pool Management

### Get Available Product Pools

Get all the product pools that are ready to buy.

- **URL**: `/product-pools`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Query Parameters:**
- `productType`: Filter by product type (`cards`, `wearables`, `stationaries`, `stickers`, `bundles`, `customize`, `accessories`)
- `status`: Filter by status (`ready_for_sale`, `selling`)
- `hasAvailableProducts`: Boolean filter for availability
- `manufacturerBid`: Filter by manufacturer
- `batchNumber`: Filter by batch number

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "poolId": "POOL-001",
      "productType": "cards",
      "status": "ready_for_sale",
      "totalQuantity": 1000,
      "availableQuantity": 850,
      "allocatedQuantity": 150,
      "activatedQuantity": 75,
      "pricing": {
        "unitPrice": 25.00,
        "currency": "USD"
      },
      "manufacturingOrder": {
        "batchNumber": "BATCH-001",
        "manufacturer": "TechCorp Manufacturing"
      },
      "sharedImages": {
        "productImages": [
          {
            "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-black-front.jpg",
            "alt": "MyCard Black Front View",
            "isPrimary": true
          },
          {
            "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-black-back.jpg",
            "alt": "MyCard Black Back View",
            "isPrimary": false
          }
        ],
        "activationPreviewUrl": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-activation-preview.jpg",
        "templateId": "template_001"
      },
      "imageOptimization": {
        "webpUrls": {
          "thumbnail": "https://res.cloudinary.com/myprofile/image/upload/c_thumb,w_150,h_150,f_webp/products/mycard-black-front.jpg",
          "medium": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_500,f_webp/products/mycard-black-front.jpg",
          "large": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_1200,f_webp/products/mycard-black-front.jpg"
        },
        "jpegUrls": {
          "thumbnail": "https://res.cloudinary.com/myprofile/image/upload/c_thumb,w_150,h_150,f_jpg/products/mycard-black-front.jpg",
          "medium": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_500,f_jpg/products/mycard-black-front.jpg",
          "large": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_1200,f_jpg/products/mycard-black-front.jpg"
        }
      }
    }
  ]
}
```

### Search Product Pools

Search through product pools.

- **URL**: `/product-pools/search`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Query Parameters:**
- `q`: Search query (minimum 2 characters)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "poolId": "POOL-002",
      "productType": "wearables",
      "status": "ready_for_sale",
      "availableQuantity": 500,
      "pricing": {
        "unitPrice": 15.00,
        "currency": "USD"
      }
    }
  ]
}
```

### Get Product Pool Details

Get detailed information about a specific product pool.

- **URL**: `/product-pools/:poolId`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Response:**
```json
{
  "success": true,
  "data": {
    "poolId": "POOL-001",
    "productType": "cards",
    "status": "ready_for_sale",
    "totalQuantity": 1000,
    "availableQuantity": 850,
    "allocatedQuantity": 150,
    "activatedQuantity": 75,
    "defectiveQuantity": 5,
    "pricing": {
      "unitPrice": 25.00,
      "currency": "USD",
      "discounts": []
    },
    "specifications": {
      "material": "Premium PVC",
      "color": "Black",
      "style": "Matte Finish",
      "dimensions": "85.6mm x 53.98mm"
    },
    "manufacturingOrder": {
      "batchNumber": "BATCH-001",
      "manufacturer": "TechCorp Manufacturing",
      "orderDate": "2024-01-01T00:00:00.000Z",
      "completionDate": "2024-01-10T00:00:00.000Z"
    },
    "sharedImages": {
      "productImages": [
        {
          "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-black-front.jpg",
          "alt": "MyCard Black Front View",
          "isPrimary": true
        },
        {
          "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-black-back.jpg",
          "alt": "MyCard Black Back View",
          "isPrimary": false
        }
      ],
      "activationPreviewUrl": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-activation-preview.jpg",
      "templateId": "template_001",
      "variantHash": "black_classic_standard"
    },
    "imageOptimization": {
      "webpUrls": {
        "thumbnail": "https://res.cloudinary.com/myprofile/image/upload/c_thumb,w_150,h_150,f_webp/products/mycard-black-front.jpg",
        "medium": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_500,f_webp/products/mycard-black-front.jpg",
        "large": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_1200,f_webp/products/mycard-black-front.jpg"
      },
      "jpegUrls": {
        "thumbnail": "https://res.cloudinary.com/myprofile/image/upload/c_thumb,w_150,h_150,f_jpg/products/mycard-black-front.jpg",
        "medium": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_500,f_jpg/products/mycard-black-front.jpg",
        "large": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_1200,f_jpg/products/mycard-black-front.jpg"
      },
      "avifUrls": {
        "thumbnail": "https://res.cloudinary.com/myprofile/image/upload/c_thumb,w_150,h_150,f_avif/products/mycard-black-front.jpg",
        "medium": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_500,f_avif/products/mycard-black-front.jpg",
        "large": "https://res.cloudinary.com/myprofile/image/upload/c_scale,w_1200,f_avif/products/mycard-black-front.jpg"
      }
    },
    "qualityMetrics": {
      "passRate": 99.5,
      "defectRate": 0.5,
      "averageQualityScore": 9.2
    }
  }
}
```

### Allocate Product from Pool

Allocate a product from a pool for purchase.

- **URL**: `/product-pools/:poolId/allocate`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "customerId": "customer_123456"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "productId": "MYC-BATCH-001-0001",
    "uniqueCode": "A1B2C3D4",
    "status": "allocated",
    "productType": "cards",
    "poolId": "POOL-001",
    "activationLink": "https://getmyprofile.online/activate/A1B2C3D4",
    "productSpecs": {
      "name": "Premium Smart Card",
      "material": "Premium PVC",
      "color": "Black",
      "style": ["Classic", "Matte"],
      "size": "Standard"
    },
    "personalizedImages": {
      "activationImageUrl": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/allocated/A1B2C3D4-activation.jpg",
      "qrCodeImageUrl": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/qrcodes/A1B2C3D4-qr.png",
      "generatedAt": "2024-01-15T10:30:00.000Z"
    },
    "allocation": {
      "allocatedAt": "2024-01-15T10:30:00.000Z",
      "customerId": "customer_123456"
    }
  },
  "message": "Product allocated successfully"
}
```

### Get Pool Statistics

Get statistical information about a product pool.

- **URL**: `/product-pools/:poolId/stats`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "data": {
    "totalProducts": 1000,
    "availableProducts": 850,
    "allocatedProducts": 150,
    "activatedProducts": 75,
    "defectiveProducts": 5,
    "activationRate": 50.0,
    "qualityScore": 9.2,
    "averageActivationTime": "2.5 days",
    "topActivationLocations": [
      {
        "country": "US",
        "count": 45
      },
      {
        "country": "CA",
        "count": 20
      }
    ]
  }
}
```

## QR Code Management

### Get QR Code by ID

Retrieve QR code information by its ID.

- **URL**: `/qrcodes/:id`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "status": "success",
  "data": {
    "id": "qr_123456",
    "productId": "MYC-BATCH-001-0001",
    "qrCodeData": "https://getmyprofile.online/activate/A1B2C3D4",
    "style": {
      "size": 300,
      "errorCorrectionLevel": "M",
      "margin": 4,
      "color": {
        "dark": "#000000",
        "light": "#FFFFFF"
      }
    },
    "scanCount": 15,
    "lastScanned": "2024-01-15T10:30:00.000Z",
    "createdAt": "2024-01-10T00:00:00.000Z"
  }
}
```

### Record QR Code Scan

Record a scan event for analytics.

- **URL**: `/qrcodes/:id/scan`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "location": {
    "lat": 40.7128,
    "lng": -74.0060
  },
  "deviceInfo": {
    "userAgent": "Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)",
    "platform": "iOS",
    "vendor": "Apple",
    "language": "en-US",
    "screenSize": {
      "width": 375,
      "height": 812
    }
  },
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

**Response:**
```json
{
  "status": "success",
  "message": "Scan recorded successfully"
}
```

### Get QR Code Statistics

Get scan statistics for a QR code.

- **URL**: `/qrcodes/:id/stats`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "status": "success",
  "data": {
    "totalScans": 15,
    "uniqueScans": 12,
    "scansByDate": [
      {
        "date": "2024-01-15",
        "count": 3
      }
    ],
    "scansByLocation": [
      {
        "country": "US",
        "city": "New York",
        "count": 8
      }
    ],
    "scansByDevice": [
      {
        "platform": "iOS",
        "count": 10
      },
      {
        "platform": "Android",
        "count": 5
      }
    ],
    "averageScansPerDay": 2.1,
    "peakScanTime": "14:30"
  }
}
```

### Get QR Codes for Product

Get all QR codes associated with a specific product.

- **URL**: `/qrcodes/product/:id`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "status": "success",
  "data": [
    {
      "id": "qr_123456",
      "qrCodeData": "https://getmyprofile.online/activate/A1B2C3D4",
      "scanCount": 15,
      "lastScanned": "2024-01-15T10:30:00.000Z"
    }
  ]
}
```

## In-Use Product Management

### Get User's Products

Retrieve products owned by the authenticated user.

- **URL**: `/in-use-products`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)
- `sort`: Sort field (default: "createdAt")
- `order`: Sort order ("asc" or "desc", default: "desc")
- `search`: Search query
- `status`: Filter by status ("active", "inactive", "pending")
- `category`: Filter by category
- `startDate`: Filter from date
- `endDate`: Filter to date

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "product_123",
      "name": "My Smart Card",
      "material": "Premium PVC",
      "category": "cards",
      "productId": "MYC-BATCH-001-0001",
      "orderId": "MO-001",
      "profileId": "profile_60d21b4667d0d8992e610c81",
      "status": "active",
      "visibility": true,
      "activator": {
        "name": "John Doe",
        "date": "2024-01-15T14:30:00.000Z"
      },
      "specifications": {
        "color": "Black",
        "style": "Classic",
        "size": "Standard"
      },
      "analytics": {
        "totalScans": 25,
        "uniqueScans": 18,
        "lastScanned": "2024-01-15T16:45:00.000Z"
      },
      "createdAt": "2024-01-15T14:30:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 3,
    "pages": 1
  }
}
```

### Create In-Use Product

Create a new in-use product record (typically after activation).

- **URL**: `/in-use-products`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "name": "My Smart Card",
  "material": "Premium PVC",
  "category": "cards",
  "productId": "MYC-BATCH-001-0001",
  "orderId": "MO-001",
  "profileId": "profile_60d21b4667d0d8992e610c81",
  "activator": {
    "name": "John Doe",
    "date": "2024-01-15T14:30:00.000Z"
  },
  "color": "Black",
  "style": "Classic",
  "size": "Standard",
  "description": "Premium smart card with NFC technology",
  "status": "active",
  "visibility": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "product_123",
    "name": "My Smart Card",
    "material": "Premium PVC",
    "category": "cards",
    "productId": "MYC-BATCH-001-0001",
    "status": "active",
    "visibility": true,
    "createdAt": "2024-01-15T14:30:00.000Z"
  }
}
```

### Get Product by ID

Retrieve a specific in-use product by its ID.

- **URL**: `/in-use-products/:id`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "product_123",
    "name": "My Smart Card",
    "material": "Premium PVC",
    "category": "cards",
    "productId": "MYC-BATCH-001-0001",
    "orderId": "MO-001",
    "profileId": "profile_60d21b4667d0d8992e610c81",
    "status": "active",
    "visibility": true,
    "activator": {
      "name": "John Doe",
      "date": "2024-01-15T14:30:00.000Z"
    },
    "specifications": {
      "color": "Black",
      "style": "Classic",
      "size": "Standard",
      "description": "Premium smart card with NFC technology"
    },
    "analytics": {
      "totalScans": 25,
      "uniqueScans": 18,
      "lastScanned": "2024-01-15T16:45:00.000Z",
      "averageScansPerDay": 3.2,
      "topScanLocations": [
        {
          "country": "US",
          "city": "New York",
          "count": 15
        }
      ]
    },
    "createdAt": "2024-01-15T14:30:00.000Z",
    "updatedAt": "2024-01-15T16:45:00.000Z"
  }
}
```

### Update Product

Update an in-use product's information.

- **URL**: `/in-use-products/:id`
- **Method**: `PUT`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "name": "My Updated Smart Card",
  "description": "Updated premium smart card with enhanced features",
  "visibility": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "product_123",
    "name": "My Updated Smart Card",
    "description": "Updated premium smart card with enhanced features",
    "visibility": false,
    "updatedAt": "2024-01-15T17:00:00.000Z"
  }
}
```

### Update Product Status

Update the status of an in-use product.

- **URL**: `/in-use-products/:id/status`
- **Method**: `PUT`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "status": "inactive"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "product_123",
    "status": "inactive",
    "updatedAt": "2024-01-15T17:00:00.000Z"
  }
}
```

### Toggle Product Visibility

Toggle the visibility status of a product.

- **URL**: `/in-use-products/:id/visibility`
- **Method**: `PUT`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "product_123",
    "visibility": false,
    "updatedAt": "2024-01-15T17:00:00.000Z"
  }
}
```

### Delete Product

Delete an in-use product.

- **URL**: `/in-use-products/:id`
- **Method**: `DELETE`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "message": "Product deleted successfully"
}
```

### Get Product Statistics

Get statistical overview of in-use products.

- **URL**: `/in-use-products/stats`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "data": {
    "totalProducts": 15,
    "activeProducts": 12,
    "inactiveProducts": 2,
    "pendingProducts": 1,
    "totalScans": 450,
    "averageScansPerProduct": 30,
    "topCategories": [
      {
        "category": "cards",
        "count": 8
      },
      {
        "category": "wearables",
        "count": 5
      }
    ],
    "recentActivity": [
      {
        "productId": "product_123",
        "action": "scan",
        "timestamp": "2024-01-15T16:45:00.000Z"
      }
    ]
  }
}
```

## Product Configuration

### Get Materials

Get available materials for product configuration.

- **URL**: `/materials`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Query Parameters:**
- `category`: Filter by category
- `grade`: Filter by grade
- `compatible`: Filter compatible materials for product type

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "mat_001",
      "name": "Premium PVC",
      "category": "plastic",
      "grade": "premium",
      "properties": {
        "durability": "high",
        "flexibility": "medium",
        "temperature_resistance": "standard"
      },
      "pricing": {
        "basePrice": 2.50,
        "currency": "USD",
        "unit": "per_card"
      },
      "compatibility": ["mycard", "mytag"],
      "images": [
        "https://res.cloudinary.com/myprofile/image/upload/v1234567890/materials/premium-pvc-sample.jpg",
        "https://res.cloudinary.com/myprofile/image/upload/v1234567890/materials/premium-pvc-texture.jpg"
      ],
      "datasheets": [
        "https://res.cloudinary.com/myprofile/raw/upload/v1234567890/materials/premium-pvc-datasheet.pdf"
      ],
      "isActive": true
    }
  ]
}
```

### Get Categories

Get available product categories.

- **URL**: `/categories`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "cat_001",
      "name": "Smart Cards",
      "code": "mycard",
      "description": "NFC-enabled smart cards for digital profiles",
      "specifications": {
        "dimensions": "85.6mm x 53.98mm",
        "thickness": "0.76mm",
        "technology": "NFC Type 2"
      },
      "pricing": {
        "basePrice": 25.00,
        "currency": "USD"
      },
      "isActive": true
    }
  ]
}
```

### Get Product Variants

Get available product variants (colors, styles, sizes).

- **URL**: `/product-variants`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Query Parameters:**
- `type`: Filter by variant type (color, style, size)
- `category`: Filter by product category

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "var_001",
      "name": "Matte Black",
      "type": "color",
      "code": "matte_black",
      "category": "cards",
      "pricing": {
        "additionalCost": 0.00,
        "currency": "USD"
      },
      "isActive": true,
      "isDefault": true
    },
    {
      "id": "var_002",
      "name": "Glossy White",
      "type": "color",
      "code": "glossy_white",
      "category": "cards",
      "pricing": {
        "additionalCost": 2.00,
        "currency": "USD"
      },
      "isActive": true,
      "isDefault": false
    }
  ]
}
```

## Product Images

### Image Storage and Management

We use **Cloudinary** for all product images. Everything gets optimized automatically and organized in folders.

### Image Types

#### 1. **Shared Pool Images** (`sharedImages`)
Template images that all products in a pool share:

- **Product Images**: Multiple views of the product (front, back, side views)
- **Activation Preview**: Image shown during the activation process
- **Template Images**: Base templates used for personalization

#### 2. **Personalized Product Images** (`personalizedImages`)
Custom images we generate for each specific product:

- **Activation Image**: Customized image for the specific product activation
- **Profile Integration Image**: Image showing how the product integrates with user profiles
- **QR Code Image**: Generated QR code image for the specific product
- **Custom Logo**: User-uploaded logos or customizations

#### 3. **Optimized Image Formats**
We automatically convert images to different formats:

- **WebP**: Modern format for web browsers (smaller file sizes)
- **JPEG**: Universal compatibility format
- **AVIF**: Next-generation format for supported browsers

#### 4. **Image Sizes**
Every image comes in different sizes:

- **Thumbnail**: 150x150px for lists and previews
- **Medium**: 500px width for product cards
- **Large**: 1200px width for detailed views
- **Print**: High-resolution for manufacturing

### Image URL Structure

```
https://res.cloudinary.com/myprofile/image/upload/[transformations]/[folder]/[filename]
```

**Examples:**
```
# Original image
https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-black-front.jpg

# Thumbnail WebP
https://res.cloudinary.com/myprofile/image/upload/c_thumb,w_150,h_150,f_webp/products/mycard-black-front.jpg

# Medium JPEG with quality optimization
https://res.cloudinary.com/myprofile/image/upload/c_scale,w_500,q_auto,f_jpg/products/mycard-black-front.jpg
```

### Image Response Format

When retrieving products, images are returned in this structure:

```json
{
  "sharedImages": {
    "productImages": [
      {
        "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/products/mycard-black-front.jpg",
        "alt": "MyCard Black Front View",
        "isPrimary": true
      }
    ],
    "activationPreviewUrl": "https://...",
    "templateId": "template_001",
    "variantHash": "black_classic_standard"
  },
  "imageOptimization": {
    "webpUrls": {
      "thumbnail": "https://...",
      "medium": "https://...",
      "large": "https://..."
    },
    "jpegUrls": { /* ... */ },
    "avifUrls": { /* ... */ }
  },
  "personalizedImages": {
    "activationImageUrl": "https://...",
    "qrCodeImageUrl": "https://...",
    "customLogoUrl": "https://...",
    "generatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

### Frontend Tips

#### 1. **Progressive Image Loading**
```javascript
// Use optimized formats with fallbacks
const imageUrl = product.imageOptimization?.webpUrls?.medium ||
                 product.imageOptimization?.jpegUrls?.medium ||
                 product.sharedImages?.productImages?.[0]?.url;
```

#### 2. **Responsive Images**
```html
<picture>
  <source
    srcset="https://res.cloudinary.com/myprofile/image/upload/c_scale,w_300,f_webp/products/image.jpg 300w,
            https://res.cloudinary.com/myprofile/image/upload/c_scale,w_600,f_webp/products/image.jpg 600w"
    type="image/webp">
  <img
    src="https://res.cloudinary.com/myprofile/image/upload/c_scale,w_600,f_jpg/products/image.jpg"
    alt="Product image"
    loading="lazy">
</picture>
```

#### 3. **Image Caching**
- CDN handles caching automatically


### Image Upload (Admin Only)

Admins upload images through the dashboard using **UploadThing**, which handles the Cloudinary integration. Supports:

- **Multiple files** (up to 30 per batch)
- **Auto optimization** and format conversion
- **Template generation** for variants
- **Batch processing** for manufacturing

## Error Handling

### Common Error Codes

| Status Code | Error Type | Description |
|-------------|------------|-------------|
| 400 | Bad Request | Invalid request parameters or body |
| 401 | Unauthorized | Authentication required or invalid token |
| 403 | Forbidden | Insufficient permissions |
| 404 | Not Found | Resource not found |
| 409 | Conflict | Resource already exists or conflict |
| 422 | Validation Error | Request validation failed |
| 429 | Rate Limited | Too many requests |
| 500 | Internal Error | Server error |

### Error Response Examples

**Validation Error:**
```json
{
  "success": false,
  "message": "Validation failed",
  "errors": [
    {
      "field": "amount",
      "message": "Amount must be positive"
    }
  ]
}
```

**Authentication Error:**
```json
{
  "success": false,
  "message": "Authentication required",
  "error": "Invalid or missing JWT token"
}
```

**Not Found Error:**
```json
{
  "success": false,
  "message": "Product not found",
  "error": "No product found with ID: product_123"
}
```

## Rate Limiting

Don't spam the API - we have limits:

- **Product Pool Operations**: 30 requests per minute per user
- **QR Code Operations**: 60 requests per minute per user
- **Product Management**: 100 requests per minute per user
- **Configuration Queries**: 120 requests per minute per user

Rate limit headers are included in responses:
```http
X-RateLimit-Limit: 60
X-RateLimit-Remaining: 45
X-RateLimit-Reset: 1642262400
```

## Search & Discovery

### Search Profiles

Search for user profiles across the platform.

- **URL**: `/search/profiles`
- **Method**: `GET`
- **Authentication**: Optional (better results when authenticated)

**Query Parameters:**
- `query`: Search query string (required)
- `page`: Page number (default: 1)
- `limit`: Results per page (default: 10)
- `filters`: JSON string with additional filters

**Response:**
```json
{
  "success": true,
  "data": {
    "profiles": [
      {
        "id": "profile_123",
        "displayName": "John Doe",
        "username": "johndoe",
        "profileImage": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/profiles/john-doe.jpg",
        "bio": "Software Developer",
        "location": "New York, USA",
        "isVerified": true
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "pages": 3
    }
  }
}
```

## Product Reviews

### Get Product Reviews

Get reviews for a specific product.

- **URL**: `/product-reviews/product/:productId`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Path Parameters:**
- `productId`: MongoDB ObjectId of the InUseProduct (required)

**Query Parameters:**
- `limit`: Number of reviews per page (1-100, default: 10)
- `skip`: Number of reviews to skip (default: 0)
- `sortBy`: Sort order (`helpful`, `recent`, `rating`)
- `filterBy`: Filter type (`all`, `verified`, `withMedia`)

**Note:** The `productId` should be the MongoDB ObjectId of an InUseProduct document, not the string productId like "MYC-BATCH-001-0001". To get reviews for a specific product, you first need to find the InUseProduct document and use its `_id` field.

**Response:**
```json
{
  "success": true,
  "data": {
    "reviews": [
      {
        "id": "review_123",
        "rating": 5,
        "title": "Excellent product!",
        "content": "Really happy with the quality and design. Fast shipping too.",
        "author": {
          "name": "John D.",
          "isVerified": true,
          "isAnonymous": false
        },
        "helpfulCount": 12,
        "media": [
          {
            "type": "image",
            "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/reviews/photo.jpg"
          }
        ],
        "createdAt": "2024-01-15T10:30:00.000Z"
      }
    ],
    "stats": {
      "totalReviews": 45,
      "averageRating": 4.2,
      "ratingDistribution": {
        "5": 20,
        "4": 15,
        "3": 7,
        "2": 2,
        "1": 1
      }
    }
  }
}
```

### Get Product Review Stats

Get statistical overview of reviews for a product.

- **URL**: `/product-reviews/stats/:productId`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Response:**
```json
{
  "success": true,
  "data": {
    "totalReviews": 45,
    "averageRating": 4.2,
    "ratingDistribution": {
      "5": 20,
      "4": 15,
      "3": 7,
      "2": 2,
      "1": 1
    },
    "verifiedPurchases": 38,
    "withMedia": 12,
    "recentTrend": "positive"
  }
}
```

### Create Product Review

Create a new review for a product.

- **URL**: `/product-reviews`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "productId": "product_123",
  "rating": 5,
  "title": "Great product!",
  "content": "Really satisfied with the quality and design. Would recommend to others.",
  "media": [
    {
      "type": "image",
      "url": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/reviews/my-photo.jpg"
    }
  ],
  "isAnonymous": false
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "review_123",
    "rating": 5,
    "title": "Great product!",
    "content": "Really satisfied with the quality and design. Would recommend to others.",
    "status": "approved",
    "createdAt": "2024-01-15T10:30:00.000Z"
  }
}
```

**Note:** Reviews are automatically approved and will appear immediately in the product review list.

### Update Product Review

Update an existing review (owner only).

- **URL**: `/product-reviews/:reviewId`
- **Method**: `PATCH`
- **Authentication**: Required (JWT, Owner only)

**Request Body:**
```json
{
  "rating": 4,
  "title": "Updated title",
  "content": "Updated review content",
  "media": []
}
```

### Mark Review as Helpful

Mark a review as helpful.

- **URL**: `/product-reviews/:reviewId/helpful`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "data": {
    "helpfulCount": 13,
    "userMarkedHelpful": true
  }
}
```

## Manufacturers

### Get All Manufacturers

Get list of all manufacturers.

- **URL**: `/manufacturers`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "mfg_001",
      "name": "TechCorp Manufacturing",
      "description": "Premium technology product manufacturer",
      "location": {
        "country": "USA",
        "city": "San Francisco",
        "address": "123 Tech Street"
      },
      "contact": {
        "email": "<EMAIL>",
        "phone": "******-0123"
      },
      "certifications": ["ISO 9001", "ISO 14001"],
      "specialties": ["Smart Cards", "NFC Devices"],
      "isActive": true
    }
  ]
}
```

### Search Manufacturers

Search for manufacturers by name or specialty.

- **URL**: `/manufacturers/search`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Query Parameters:**
- `q`: Search query (required)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "mfg_001",
      "name": "TechCorp Manufacturing",
      "specialties": ["Smart Cards", "NFC Devices"],
      "location": {
        "country": "USA",
        "city": "San Francisco"
      }
    }
  ]
}
```

### Get Manufacturer Details

Get detailed information about a specific manufacturer.

- **URL**: `/manufacturers/:id`
- **Method**: `GET`
- **Authentication**: None (Public endpoint)

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "mfg_001",
    "name": "TechCorp Manufacturing",
    "description": "Premium technology product manufacturer",
    "location": {
      "country": "USA",
      "city": "San Francisco",
      "address": "123 Tech Street",
      "zipCode": "94105"
    },
    "contact": {
      "email": "<EMAIL>",
      "phone": "******-0123",
      "website": "https://techcorp.com"
    },
    "certifications": ["ISO 9001", "ISO 14001"],
    "specialties": ["Smart Cards", "NFC Devices"],
    "productionCapacity": {
      "monthly": 50000,
      "leadTime": "2-3 weeks"
    },
    "qualityMetrics": {
      "defectRate": 0.02,
      "onTimeDelivery": 98.5
    },
    "isActive": true,
    "createdAt": "2023-01-01T00:00:00.000Z"
  }
}
```

## Additional Product Pool Endpoints

### Get Low Stock Pools

Get product pools that are running low on inventory.

- **URL**: `/product-pools/low-stock`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Query Parameters:**
- `threshold`: Stock threshold (default: 10)

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "poolId": "POOL-001",
      "productType": "mycard",
      "availableQuantity": 8,
      "totalQuantity": 1000,
      "threshold": 10,
      "status": "low_stock",
      "lastRestocked": "2024-01-01T00:00:00.000Z"
    }
  ]
}
```

## Additional QR Code Endpoints

### Get All QR Codes

Get all QR codes with filtering options.

- **URL**: `/qrcodes`
- **Method**: `GET`
- **Authentication**: Required (JWT)

**Query Parameters:**
- `productId`: Filter by product ID
- `status`: Filter by status
- `limit`: Results per page
- `page`: Page number

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "qr_123456",
      "productId": "MYC-BATCH-001-0001",
      "qrCodeData": "https://getmyprofile.online/activate/A1B2C3D4",
      "scanCount": 15,
      "lastScanned": "2024-01-15T10:30:00.000Z",
      "status": "active",
      "createdAt": "2024-01-10T00:00:00.000Z"
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 150,
    "pages": 15
  }
}
```

### Generate Bulk QR Codes

Generate multiple QR codes at once.

- **URL**: `/qrcodes/bulk`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "qrCodes": [
    {
      "productId": "MYC-BATCH-001-0001",
      "qrCodeData": "https://getmyprofile.online/activate/A1B2C3D4",
      "style": {
        "size": 300,
        "errorCorrectionLevel": "M"
      }
    }
  ]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "count": 1,
    "qrCodes": [
      {
        "id": "qr_123456",
        "productId": "MYC-BATCH-001-0001",
        "qrCodeData": "https://getmyprofile.online/activate/A1B2C3D4",
        "imageUrl": "https://res.cloudinary.com/myprofile/image/upload/v1234567890/qrcodes/A1B2C3D4-qr.png"
      }
    ]
  }
}
```

### Delete QR Code

Delete a QR code.

- **URL**: `/qrcodes/:id`
- **Method**: `DELETE`
- **Authentication**: Required (JWT)

**Response:**
```json
{
  "success": true,
  "message": "QR code deleted successfully"
}
```

## Additional In-Use Products Endpoints

### Bulk Delete Products

Delete multiple in-use products at once.

- **URL**: `/in-use-products/bulk-delete`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "ids": ["product_123", "product_456", "product_789"]
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "deletedCount": 3,
    "deletedIds": ["product_123", "product_456", "product_789"]
  }
}
```

### Bulk Update Product Status

Update status of multiple products at once.

- **URL**: `/in-use-products/bulk-status`
- **Method**: `POST`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "ids": ["product_123", "product_456"],
  "status": "inactive"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "updatedCount": 2,
    "updatedIds": ["product_123", "product_456"],
    "newStatus": "inactive"
  }
}
```

### Update Product Review

Update review information for a product.

- **URL**: `/in-use-products/:id/review`
- **Method**: `PUT`
- **Authentication**: Required (JWT)

**Request Body:**
```json
{
  "rating": 5,
  "review": "Excellent product, very satisfied with the quality.",
  "wouldRecommend": true
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "product_123",
    "review": {
      "rating": 5,
      "review": "Excellent product, very satisfied with the quality.",
      "wouldRecommend": true,
      "updatedAt": "2024-01-15T17:00:00.000Z"
    }
  }
}
```

## Product Analytics

### Get Product Analytics

Get analytics data for products owned by a profile.

- **URL**: `/analytics/dashboard/:profileId/products`
- **Method**: `GET`
- **Authentication**: Required (JWT + Profile Access)

**Response:**
```json
{
  "success": true,
  "data": {
    "totalProducts": 5,
    "activeProducts": 4,
    "totalScans": 150,
    "uniqueScans": 120,
    "scanTrends": [
      {
        "date": "2024-01-15",
        "scans": 25
      }
    ],
    "topProducts": [
      {
        "productId": "product_123",
        "name": "My Smart Card",
        "scans": 45,
        "uniqueScans": 38
      }
    ],
    "geographicData": [
      {
        "country": "US",
        "scans": 80
      }
    ]
  }
}
```

## Best Practices

### Authentication
- Always send JWT tokens
- Use profile tokens when you have them
- Handle expired tokens gracefully

### Error Handling
- Check the `success` field first
- Handle different error types properly
- Retry rate-limited requests with backoff

### Performance
- Use pagination - don't load everything at once
- Cache stuff that doesn't change often
- Show loading states so users know what's happening

### Security
- Validate inputs on your end too
- HTTPS only, obviously
- Store tokens in HTTP-only cookies if possible
