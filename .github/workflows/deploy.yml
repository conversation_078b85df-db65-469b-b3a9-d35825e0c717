name: Deploy TypeScript App to VPS

on:
  push:
    branches: [main]

env:
  DROPLET_IP: ${{ secrets.DROPLET_IP }}
  DROPLET_USERNAME: ${{ secrets.DROPLET_USERNAME }}
  PROJECT_ROOT: ${{ secrets.PROJECT_ROOT }}
  PM2_APP_NAME: ${{ secrets.PM2_APP_NAME || 'server' }}
  TEST_MODE: ${{ secrets.TEST_MODE || 'true' }}
  NODE_MEMORY_LIMIT: 8192  # Doubled memory limit

jobs:
  deploy:
    runs-on: ubuntu-latest
    timeout-minutes: 30  # Increased timeout

    steps:
    - uses: actions/checkout@v4

    - name: Setup SSH
      uses: shimataro/ssh-key-action@v2
      with:
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        known_hosts: ${{ secrets.KNOW_HOSTS }}

    - uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Silent install
      run: |
        echo "Installing dependencies silently..."
        npm config set fund false
        npm set audit false
        npm install --no-audit --no-fund --loglevel=error > /dev/null 2>&1 || true

    - name: Optimized build
      run: |
        echo "Building with ${NODE_MEMORY_LIMIT}MB memory limit..."
        export NODE_OPTIONS="--max-old-space-size=$NODE_MEMORY_LIMIT"
        
        # Split build into smaller chunks if needed
        npm run build --if-present || \
        (echo "Fallback build strategy..." && \
         npx tsc --skipLibCheck && \
         cp -r src/templates dist/templates 2>/dev/null || true && \
         cp -r public dist/public 2>/dev/null || true)
        
        # Cleanup
        rm -rf node_modules src *.ts tsconfig.json .github/ .gitignore .env* test/

    - name: Deploy artifacts
      run: |
        echo "Deploying to server..."
        rsync -avz --delete \
          --exclude={'node_modules','.git','.env*','.github','src','*.ts','tsconfig.json','test'} \
          -e "ssh -o StrictHostKeyChecking=no" \
          ./ ${{ secrets.DROPLET_USERNAME }}@${{ secrets.DROPLET_IP }}:${{ secrets.PROJECT_ROOT }}

    - name: Production setup
      run: |
        echo "Setting up production..."
        ssh -o StrictHostKeyChecking=no \
          ${{ secrets.DROPLET_USERNAME }}@${{ secrets.DROPLET_IP }} \
          "cd ${{ secrets.PROJECT_ROOT }} && \
          npm install --omit=dev --ignore-scripts --no-audit --no-fund --loglevel=error > /dev/null 2>&1 && \
          TEST_MODE=${{ env.TEST_MODE }} pm2 reload ${{ secrets.PM2_APP_NAME }} --update-env && \
          pm2 save"

    - name: Verify deployment
      run: |
        echo "Verifying deployment..."
        ssh -o StrictHostKeyChecking=no \
          ${{ secrets.DROPLET_USERNAME }}@${{ secrets.DROPLET_IP }} \
          "pm2 list && \
          echo 'Node.js version: $(node -v)' && \
          echo '✅ Deployment successful with ${NODE_MEMORY_LIMIT}MB memory allocation'"
