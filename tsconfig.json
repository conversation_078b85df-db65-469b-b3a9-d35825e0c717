{"compilerOptions": {"target": "es2018", "module": "commonjs", "lib": ["es2018", "esnext.asynciterable"], "downlevelIteration": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "moduleResolution": "node", "resolveJsonModule": true, "baseUrl": ".", "paths": {"*": ["node_modules/*"]}, "typeRoots": ["node_modules/@types", "src/types", "tests"]}, "include": ["src/**/*", "src/types/**/*.d.ts", "../src/models/analytics/DailyAnalytics.ts", "../src/models/analytics/MonthlyAnalytics.ts", "../src/models/analytics/RealTimeMetrics.ts"], "exclude": ["node_modules", "dist", "src/tests", "**/*.test.ts", "**/*.spec.ts"]}