#!/usr/bin/env node

const mongoose = require('mongoose');

console.log('Testing basic MongoDB connection...');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/my-profile-dev';
console.log('Using connection string:', MONGODB_URI);

mongoose.connect(MONGODB_URI)
  .then(() => {
    console.log('✅ Connected to MongoDB successfully');
    return mongoose.connection.db.listCollections().toArray();
  })
  .then(collections => {
    console.log('📂 Available collections:', collections.map(c => c.name));
    return mongoose.disconnect();
  })
  .then(() => {
    console.log('🔌 Disconnected successfully');
    process.exit(0);
  })
  .catch(error => {
    console.error('❌ Connection error:', error.message);
    process.exit(1);
  });