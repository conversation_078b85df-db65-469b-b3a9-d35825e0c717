{"shopping_list_examples": {"description": "Sample JSON for shopping list operations", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "add_shopping_item": {"basic_item": {"name": "Milk", "quantity": 2, "unit": "liters", "profileId": "profile_id_here", "description": "Basic shopping item"}, "with_price": {"name": "Bread", "quantity": 3, "unit": "pieces", "pricePerUnit": 2.5, "notes": "Whole grain bread", "profileId": "profile_id_here", "description": "Shopping item with price"}, "with_notes": {"name": "Apples", "quantity": 1, "unit": "kg", "pricePerUnit": 3.99, "notes": "Organic red apples", "profileId": "profile_id_here", "description": "Shopping item with notes"}, "bulk_item": {"name": "Rice", "quantity": 10, "unit": "kg", "pricePerUnit": 15.99, "notes": "Basmati rice for the month", "profileId": "profile_id_here", "description": "Bulk shopping item"}}, "update_shopping_item": {"update_quantity": {"quantity": 5, "description": "Update quantity only"}, "update_price": {"pricePerUnit": 2.75, "description": "Update price only"}, "mark_purchased": {"purchased": true, "description": "Mark item as purchased"}, "full_update": {"name": "Organic Milk", "quantity": 3, "unit": "liters", "pricePerUnit": 2.25, "notes": "Updated notes", "purchased": false, "description": "Full item update"}}}, "inventory_examples": {"description": "Sample JSON for inventory operations", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "add_inventory_item": {"basic_item": {"name": "Rice", "quantity": 5, "unit": "kg", "profileId": "profile_id_here", "description": "Basic inventory item"}, "with_notes": {"name": "Olive Oil", "quantity": 2, "unit": "liters", "notes": "Extra virgin olive oil", "profileId": "profile_id_here", "description": "Inventory item with notes"}, "bulk_item": {"name": "Pasta", "quantity": 20, "unit": "packages", "notes": "Spaghetti and penne", "profileId": "profile_id_here", "description": "Bulk inventory item"}}, "update_inventory_item": {"update_quantity": {"quantity": 3, "description": "Update quantity only"}, "update_notes": {"notes": "Updated inventory notes", "description": "Update notes only"}, "full_update": {"name": "Organic Rice", "quantity": 8, "unit": "kg", "notes": "Updated inventory notes", "description": "Full inventory item update"}}, "update_quantity_only": {"quantity": 2, "description": "Update quantity using dedicated endpoint"}}, "api_endpoints": {"shopping_list": {"GET /api/shopping-list": "Get current shopping list", "POST /api/shopping-list": "Add new shopping item", "PATCH /api/shopping-list/:id": "Update shopping item", "PATCH /api/shopping-list/:id/purchase": "Mark item as purchased", "DELETE /api/shopping-list/:id": "Delete shopping item", "POST /api/shopping-list/move-to-inventory": "Auto-move purchased items to inventory", "GET /api/shopping-list/stats": "Get shopping list statistics"}, "inventory": {"GET /api/inventory": "List all inventory items", "POST /api/inventory": "Add new inventory item", "PATCH /api/inventory/:id": "Update inventory item", "PATCH /api/inventory/:id/quantity": "Update quantity only", "DELETE /api/inventory/:id": "Remove inventory item", "GET /api/inventory/search": "Search inventory items", "GET /api/inventory/stats": "Get inventory statistics", "GET /api/inventory/low-stock": "Get low stock items"}}, "query_parameters": {"shopping_list": {"includePurchased": "boolean (default: true) - Include purchased items in response", "profileId": "string (optional) - Filter by specific profile"}, "inventory_search": {"q": "string (required) - Search query for inventory items", "profileId": "string (optional) - Filter by specific profile"}, "inventory": {"profileId": "string (optional) - Filter by specific profile"}, "shopping_stats": {"profileId": "string (optional) - Get stats for specific profile"}, "inventory_stats": {"profileId": "string (optional) - Get stats for specific profile"}, "low_stock": {"profileId": "string (optional) - Get low stock for specific profile"}}, "curl_examples": {"shopping_list": {"get_list": "curl -X GET '/api/shopping-list?profileId=profile_id_here' -H 'Authorization: Bearer YOUR_TOKEN'", "add_item": "curl -X POST /api/shopping-list -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"name\":\"Milk\",\"quantity\":2,\"unit\":\"liters\",\"pricePerUnit\":1.50,\"notes\":\"Organic whole milk\",\"profileId\":\"profile_id_here\"}'", "update_item": "curl -X PATCH /api/shopping-list/item_id_here -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"quantity\":3,\"pricePerUnit\":1.75}'", "mark_purchased": "curl -X PATCH /api/shopping-list/item_id_here/purchase -H 'Authorization: Bearer YOUR_TOKEN'", "delete_item": "curl -X DELETE /api/shopping-list/item_id_here -H 'Authorization: Bearer YOUR_TOKEN'", "move_to_inventory": "curl -X POST /api/shopping-list/move-to-inventory -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"profileId\":\"profile_id_here\"}'", "get_stats": "curl -X GET '/api/shopping-list/stats?profileId=profile_id_here' -H 'Authorization: Bearer YOUR_TOKEN'"}, "inventory": {"get_inventory": "curl -X GET '/api/inventory?profileId=profile_id_here' -H 'Authorization: Bearer YOUR_TOKEN'", "add_item": "curl -X POST /api/inventory -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"name\":\"Rice\",\"quantity\":5,\"unit\":\"kg\",\"notes\":\"Basmati rice\",\"profileId\":\"profile_id_here\"}'", "update_item": "curl -X PATCH /api/inventory/item_id_here -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"quantity\":3,\"notes\":\"Updated notes\"}'", "update_quantity": "curl -X PATCH /api/inventory/item_id_here/quantity -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"quantity\":2}'", "delete_item": "curl -X DELETE /api/inventory/item_id_here -H 'Authorization: Bearer YOUR_TOKEN'", "search_items": "curl -X GET '/api/inventory/search?q=rice&profileId=profile_id_here' -H 'Authorization: Bearer YOUR_TOKEN'", "get_stats": "curl -X GET '/api/inventory/stats?profileId=profile_id_here' -H 'Authorization: Bearer YOUR_TOKEN'", "get_low_stock": "curl -X GET '/api/inventory/low-stock?profileId=profile_id_here' -H 'Authorization: Bearer YOUR_TOKEN'"}}, "response_examples": {"shopping_list_response": {"success": true, "data": [{"_id": "item_id_here", "name": "Milk", "quantity": 2, "unit": "liters", "pricePerUnit": 1.5, "notes": "Organic whole milk", "purchased": false, "user": "user_id_here", "profileId": "profile_id_here", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "count": 1}, "inventory_response": {"success": true, "data": [{"_id": "item_id_here", "name": "Rice", "quantity": 5, "unit": "kg", "notes": "Basmati rice", "user": "user_id_here", "profileId": "profile_id_here", "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}], "count": 1}, "shopping_stats_response": {"success": true, "data": {"totalItems": 10, "purchasedItems": 3, "pendingItems": 7, "totalValue": 45.5}}, "inventory_stats_response": {"success": true, "data": {"totalItems": 15, "totalQuantity": 50, "uniqueItemCount": 12}}, "move_to_inventory_response": {"success": true, "data": {"moved": 3, "errors": []}, "message": "Moved 3 items to inventory"}}, "workflow_examples": {"complete_shopping_workflow": {"step1_add_item": {"method": "POST", "endpoint": "/api/shopping-list", "body": {"name": "Milk", "quantity": 2, "unit": "liters", "pricePerUnit": 1.5, "notes": "Organic whole milk", "profileId": "profile_id_here"}, "description": "Add item to shopping list"}, "step2_mark_purchased": {"method": "PATCH", "endpoint": "/api/shopping-list/item_id_here/purchase", "description": "Mark item as purchased"}, "step3_move_to_inventory": {"method": "POST", "endpoint": "/api/shopping-list/move-to-inventory", "body": {"profileId": "profile_id_here"}, "description": "Move purchased items to inventory"}, "step4_check_inventory": {"method": "GET", "endpoint": "/api/inventory?profileId=profile_id_here", "description": "Check that item is now in inventory"}}, "inventory_management_workflow": {"step1_add_item": {"method": "POST", "endpoint": "/api/inventory", "body": {"name": "Rice", "quantity": 5, "unit": "kg", "notes": "Basmati rice", "profileId": "profile_id_here"}, "description": "Add item to inventory"}, "step2_update_quantity": {"method": "PATCH", "endpoint": "/api/inventory/item_id_here/quantity", "body": {"quantity": 3}, "description": "Update quantity when used"}, "step3_check_low_stock": {"method": "GET", "endpoint": "/api/inventory/low-stock?profileId=profile_id_here", "description": "Check for items that need restocking"}}}, "common_units": {"weight": ["kg", "g", "lbs", "oz"], "volume": ["liters", "ml", "gallons", "cups"], "count": ["pieces", "packages", "boxes", "bottles", "cans"], "length": ["meters", "cm", "feet", "inches"]}, "required_fields": {"shopping_item": {"name": "string (required) - Item name", "quantity": "number (required) - Item quantity", "unit": "string (required) - Unit of measurement", "profileId": "string (required) - Profile ID to associate with"}, "inventory_item": {"name": "string (required) - Item name", "quantity": "number (required) - Item quantity", "unit": "string (required) - Unit of measurement", "profileId": "string (required) - Profile ID to associate with"}}, "optional_fields": {"shopping_item": {"pricePerUnit": "number (optional) - Price per unit", "notes": "string (optional) - Additional notes"}, "inventory_item": {"notes": "string (optional) - Additional notes"}}, "error_responses": {"authentication_error": {"success": false, "message": "User not authenticated"}, "validation_error": {"success": false, "message": "Name, quantity, unit, and profileId are required"}, "not_found_error": {"success": false, "message": "Item not found or access denied"}, "quantity_error": {"success": false, "message": "Quantity cannot be negative"}, "profile_error": {"success": false, "message": "Invalid profile ID"}}}