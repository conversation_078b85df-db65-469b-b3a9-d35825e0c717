{"timestamp": "2025-07-24T15:26:24.315Z", "totalCount": 84, "data": [{"_id": "688230a55abef7df2b02c341", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Adopted Sister", "relationshipTags": ["family", "kinship", "adopted"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-06-06T12:22:54.566Z", "updatedAt": "2025-07-23T17:50:40.738Z", "__v": 0}, {"_id": "688230a55abef7df2b02c342", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Other Family", "relationshipDescription": "Family relationship: Family", "relationshipTags": ["family", "kinship"], "isSystemDefined": false, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T17:52:39.019Z", "updatedAt": "2025-07-23T17:50:41.978Z", "__v": 0}, {"_id": "688230a55abef7df2b02c343", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Social", "relationshipDescription": "Personal relationship: Acquaintance", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:42.382Z", "__v": 0}, {"_id": "688230a55abef7df2b02c344", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Friendship", "relationshipDescription": "Personal relationship: Best friend", "relationshipTags": ["personal", "social", "friendship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:42.786Z", "__v": 0}, {"_id": "688230a55abef7df2b02c345", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Friendship", "relationshipDescription": "Personal relationship: Boyfriend", "relationshipTags": ["personal", "social", "friendship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:43.191Z", "__v": 0}, {"_id": "688230a55abef7df2b02c346", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Friendship", "relationshipDescription": "Personal relationship: Girlfriend", "relationshipTags": ["personal", "social", "friendship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:43.599Z", "__v": 0}, {"_id": "688230a55abef7df2b02c347", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Friendship", "relationshipDescription": "Personal relationship: Casual friend", "relationshipTags": ["personal", "social", "friendship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:44.005Z", "__v": 0}, {"_id": "688230a55abef7df2b02c348", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Friendship", "relationshipDescription": "Personal relationship: Close friend", "relationshipTags": ["personal", "social", "friendship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:44.410Z", "__v": 0}, {"_id": "688230a55abef7df2b02c349", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Romantic", "relationshipDescription": "Personal relationship: <PERSON><PERSON><PERSON>", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:44.813Z", "__v": 0}, {"_id": "688230a55abef7df2b02c34a", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Romantic", "relationshipDescription": "Personal relationship: <PERSON><PERSON><PERSON>", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.063Z", "updatedAt": "2025-07-23T17:50:45.214Z", "__v": 0}, {"_id": "688230a55abef7df2b02c34b", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Mentorship", "relationshipDescription": "Personal relationship: <PERSON><PERSON>", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:45.617Z", "__v": 0}, {"_id": "688230a55abef7df2b02c34c", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Mentorship", "relationshipDescription": "Personal relationship: <PERSON><PERSON>", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:46.020Z", "__v": 0}, {"_id": "688230a55abef7df2b02c34d", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Social", "relationshipDescription": "Personal relationship: <PERSON><PERSON><PERSON><PERSON>", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:46.551Z", "__v": 0}, {"_id": "688230a55abef7df2b02c34e", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Romantic", "relationshipDescription": "Personal relationship: Partner", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:46.955Z", "__v": 0}, {"_id": "688230a55abef7df2b02c34f", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "subCategory": "Social", "relationshipDescription": "Personal relationship: Teammate", "relationshipTags": ["personal", "social"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:47.403Z", "__v": 0}, {"_id": "688230a55abef7df2b02c350", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Adopted brother", "relationshipTags": ["family", "kinship", "adopted"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:47.805Z", "__v": 0}, {"_id": "688230a55abef7df2b02c351", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Adopted daughter", "relationshipTags": ["family", "kinship", "adopted"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:48.216Z", "__v": 0}, {"_id": "688230a55abef7df2b02c352", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Adopted son", "relationshipTags": ["family", "kinship", "adopted"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:48.619Z", "__v": 0}, {"_id": "688230a55abef7df2b02c353", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Adopted father", "relationshipTags": ["family", "kinship", "adopted"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:49.024Z", "__v": 0}, {"_id": "688230a55abef7df2b02c354", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Adopted mother", "relationshipTags": ["family", "kinship", "adopted"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:49.496Z", "__v": 0}, {"_id": "688230a55abef7df2b02c355", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Extended Family", "relationshipDescription": "Family relationship: Aunt", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:49.898Z", "__v": 0}, {"_id": "688230a55abef7df2b02c356", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Extended Family", "relationshipDescription": "Family relationship: Uncle", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:50.302Z", "__v": 0}, {"_id": "688230a55abef7df2b02c357", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Biological brother", "relationshipTags": ["family", "kinship", "biological"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:50.705Z", "__v": 0}, {"_id": "688230a55abef7df2b02c358", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Biological sister", "relationshipTags": ["family", "kinship", "biological"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:51.108Z", "__v": 0}, {"_id": "688230a55abef7df2b02c359", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Biological daughter", "relationshipTags": ["family", "kinship", "biological"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:51.512Z", "__v": 0}, {"_id": "688230a55abef7df2b02c35a", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Biological son", "relationshipTags": ["family", "kinship", "biological"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:51.915Z", "__v": 0}, {"_id": "688230a55abef7df2b02c35b", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Biological father", "relationshipTags": ["family", "kinship", "biological"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:52.318Z", "__v": 0}, {"_id": "688230a55abef7df2b02c35c", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Biological mother", "relationshipTags": ["family", "kinship", "biological"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:52.721Z", "__v": 0}, {"_id": "688230a55abef7df2b02c35d", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Cousins", "relationshipDescription": "Family relationship: <PERSON><PERSON><PERSON> (First)", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.064Z", "updatedAt": "2025-07-23T17:50:53.127Z", "__v": 0}, {"_id": "688230a55abef7df2b02c35e", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Cousins", "relationshipDescription": "Family relationship: <PERSON><PERSON><PERSON> (Second)", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:53.531Z", "__v": 0}, {"_id": "688230a55abef7df2b02c35f", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Cousins", "relationshipDescription": "Family relationship: <PERSON><PERSON><PERSON> (Third)", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:53.945Z", "__v": 0}, {"_id": "688230a55abef7df2b02c360", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Foster brother", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:54.347Z", "__v": 0}, {"_id": "688230a55abef7df2b02c361", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Foster sister", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:54.765Z", "__v": 0}, {"_id": "688230a55abef7df2b02c362", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Foster daughter", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:55.168Z", "__v": 0}, {"_id": "688230a55abef7df2b02c363", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Foster son", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:55.570Z", "__v": 0}, {"_id": "688230a55abef7df2b02c364", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Foster father", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:55.971Z", "__v": 0}, {"_id": "688230a55abef7df2b02c365", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Foster mother", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:56.372Z", "__v": 0}, {"_id": "688230a55abef7df2b02c366", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Granddaughter", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:56.776Z", "__v": 0}, {"_id": "688230a55abef7df2b02c367", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: <PERSON><PERSON>", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:57.180Z", "__v": 0}, {"_id": "688230a55abef7df2b02c368", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Grandfather", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:57.583Z", "__v": 0}, {"_id": "688230a55abef7df2b02c369", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Grandmother", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:57.985Z", "__v": 0}, {"_id": "688230a55abef7df2b02c36a", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Spouse", "relationshipDescription": "Family relationship: Husband", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:58.387Z", "__v": 0}, {"_id": "688230a55abef7df2b02c36b", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Spouse", "relationshipDescription": "Family relationship: Wife", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:58.790Z", "__v": 0}, {"_id": "688230a55abef7df2b02c36c", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Extended Family", "relationshipDescription": "Family relationship: <PERSON><PERSON><PERSON><PERSON>", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:59.192Z", "__v": 0}, {"_id": "688230a55abef7df2b02c36d", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Extended Family", "relationshipDescription": "Family relationship: <PERSON><PERSON><PERSON>", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:59.594Z", "__v": 0}, {"_id": "688230a55abef7df2b02c36e", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: <PERSON><PERSON><PERSON>", "relationshipTags": ["family", "kinship", "step-family"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:50:59.997Z", "__v": 0}, {"_id": "688230a55abef7df2b02c36f", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Stepsister", "relationshipTags": ["family", "kinship", "step-family"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:51:00.400Z", "__v": 0}, {"_id": "688230a55abef7df2b02c370", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: Stepdaughter", "relationshipTags": ["family", "kinship", "step-family"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:51:00.806Z", "__v": 0}, {"_id": "688230a55abef7df2b02c371", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Children", "relationshipDescription": "Family relationship: <PERSON><PERSON>", "relationshipTags": ["family", "kinship", "step-family"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:51:01.211Z", "__v": 0}, {"_id": "688230a55abef7df2b02c372", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Stepfather", "relationshipTags": ["family", "kinship", "step-family"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:51:01.614Z", "__v": 0}, {"_id": "688230a55abef7df2b02c373", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "Parents", "relationshipDescription": "Family relationship: Stepmother", "relationshipTags": ["family", "kinship", "step-family"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.065Z", "updatedAt": "2025-07-23T17:51:02.016Z", "__v": 0}, {"_id": "688230a55abef7df2b02c374", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Twin brother", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:02.419Z", "__v": 0}, {"_id": "688230a55abef7df2b02c375", "relationshipTypeName": "Family", "profileCategoryType": "individual", "profileType": "family", "subCategory": "<PERSON><PERSON>s", "relationshipDescription": "Family relationship: Twin sister", "relationshipTags": ["family", "kinship"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:02.822Z", "__v": 0}, {"_id": "688230a55abef7df2b02c376", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Affiliate", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:03.226Z", "__v": 0}, {"_id": "688230a55abef7df2b02c377", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Advisory", "relationshipDescription": "Professional relationship: Advisor (Financial)", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:03.628Z", "__v": 0}, {"_id": "688230a55abef7df2b02c378", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Advisory", "relationshipDescription": "Professional relationship: Advisor (Legal)", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:04.030Z", "__v": 0}, {"_id": "688230a55abef7df2b02c379", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Association", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:04.432Z", "__v": 0}, {"_id": "688230a55abef7df2b02c37a", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Auditor", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:04.833Z", "__v": 0}, {"_id": "688230a55abef7df2b02c37b", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Board Member", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:05.235Z", "__v": 0}, {"_id": "688230a55abef7df2b02c37c", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Client Relations", "relationshipDescription": "Professional relationship: Client", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:05.638Z", "__v": 0}, {"_id": "688230a55abef7df2b02c37d", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Client Relations", "relationshipDescription": "Professional relationship: Customer", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:06.047Z", "__v": 0}, {"_id": "688230a55abef7df2b02c37e", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Competitor", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:06.449Z", "__v": 0}, {"_id": "688230a55abef7df2b02c37f", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Advisory", "relationshipDescription": "Professional relationship: Consultant", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:06.852Z", "__v": 0}, {"_id": "688230a55abef7df2b02c380", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Contractor", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:07.255Z", "__v": 0}, {"_id": "688230a55abef7df2b02c381", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Distributor", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:07.658Z", "__v": 0}, {"_id": "688230a55abef7df2b02c382", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: <PERSON><PERSON><PERSON><PERSON>", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:08.060Z", "__v": 0}, {"_id": "688230a55abef7df2b02c383", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: <PERSON><PERSON><PERSON><PERSON>", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:08.462Z", "__v": 0}, {"_id": "688230a55abef7df2b02c384", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Industry Consortium Member", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:08.864Z", "__v": 0}, {"_id": "688230a55abef7df2b02c385", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Investor", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:09.266Z", "__v": 0}, {"_id": "688230a55abef7df2b02c386", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Partnership", "relationshipDescription": "Professional relationship: Joint Venture Partner", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:09.668Z", "__v": 0}, {"_id": "688230a55abef7df2b02c387", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Licensing Authority", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:10.070Z", "__v": 0}, {"_id": "688230a55abef7df2b02c388", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Partnership", "relationshipDescription": "Professional relationship: Marketing Partner", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:10.472Z", "__v": 0}, {"_id": "688230a55abef7df2b02c389", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Partnership", "relationshipDescription": "Professional relationship: Outsourcing Partner", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:10.874Z", "__v": 0}, {"_id": "688230a55abef7df2b02c38a", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Parent Company", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:11.278Z", "__v": 0}, {"_id": "688230a55abef7df2b02c38b", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Subsidiary", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:11.680Z", "__v": 0}, {"_id": "688230a55abef7df2b02c38c", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Regulatory Body", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:12.083Z", "__v": 0}, {"_id": "688230a55abef7df2b02c38d", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Service Provider", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:12.485Z", "__v": 0}, {"_id": "688230a55abef7df2b02c38e", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Business", "relationshipDescription": "Professional relationship: Shareholder", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:12.887Z", "__v": 0}, {"_id": "688230a55abef7df2b02c38f", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Partnership", "relationshipDescription": "Professional relationship: Strategic Partner", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:13.289Z", "__v": 0}, {"_id": "688230a55abef7df2b02c390", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Vendor Relations", "relationshipDescription": "Professional relationship: Supplier", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:13.695Z", "__v": 0}, {"_id": "688230a55abef7df2b02c391", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Partnership", "relationshipDescription": "Professional relationship: Technology Partner", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.066Z", "updatedAt": "2025-07-23T17:51:14.098Z", "__v": 0}, {"_id": "688230a55abef7df2b02c392", "relationshipTypeName": "Professional", "profileCategoryType": "business", "profileType": "professional", "subCategory": "Vendor Relations", "relationshipDescription": "Professional relationship: <PERSON><PERSON><PERSON>", "relationshipTags": ["professional", "business"], "isSystemDefined": true, "status": "approved", "createdBy": "000000000000000000000000", "createdAt": "2025-07-04T19:56:29.067Z", "updatedAt": "2025-07-23T17:51:14.501Z", "__v": 0}, {"_id": "688230a55abef7df2b02c393", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "relationshipDescription": "Someone you study with", "relationshipTags": [], "isSystemDefined": false, "status": "approved", "createdBy": "681dfe398a604b8df9135b13", "createdAt": "2025-07-22T21:31:36.079Z", "updatedAt": "2025-07-23T17:51:14.903Z", "__v": 0}, {"_id": "688230a55abef7df2b02c394", "relationshipTypeName": "Personal", "profileCategoryType": "individual", "profileType": "personal", "relationshipDescription": "Someone you study with", "relationshipTags": [], "isSystemDefined": false, "status": "approved", "createdBy": "681dfe398a604b8df9135b13", "createdAt": "2025-07-22T21:35:41.627Z", "updatedAt": "2025-07-23T17:51:15.306Z", "__v": 0}]}