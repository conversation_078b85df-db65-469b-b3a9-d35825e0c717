{"sample_user_card_creation": {"description": "Sample JSON for creating a user card", "endpoint": "POST /api/user-cards", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "examples": {"basic_card": {"templateId": "template_id_here", "profileId": "profile_id_here", "description": "Basic card with minimal configuration"}, "custom_colors_card": {"templateId": "template_id_here", "profileId": "profile_id_here", "customColors": {"primary": "#FF5733", "secondary": "#33FF57", "accent": "#3357FF", "background": "#FFFFFF", "text": "#000000"}, "description": "Card with custom color scheme"}, "qr_code_card": {"templateId": "template_id_here", "profileId": "profile_id_here", "qrCodeUrl": "https://example.com/qr-code-url", "customColors": {"primary": "#FF0000"}, "description": "Card with QR code URL"}, "nfc_card": {"templateId": "template_id_here", "profileId": "profile_id_here", "nfcData": "nfc_data_string_here", "customSvg": "<svg width='100' height='100'><circle cx='50' cy='50' r='40' fill='#FF5733'/></svg>", "description": "Card with NFC data and custom SVG"}, "full_customization": {"templateId": "template_id_here", "profileId": "profile_id_here", "customColors": {"primary": "#FF5733", "secondary": "#33FF57", "accent": "#3357FF", "background": "#FFFFFF", "text": "#000000", "border": "#CCCCCC"}, "customSvg": "<svg width='200' height='100'><rect width='200' height='100' fill='#FF5733'/><text x='100' y='50' text-anchor='middle' fill='white'>Custom Card</text></svg>", "qrCodeUrl": "https://example.com/qr-code", "nfcData": "nfc_data_string_here", "description": "Fully customized card with all features"}}}, "sample_card_update": {"description": "Sample JSON for updating a user card", "endpoint": "PUT /api/user-cards/:id", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "examples": {"update_colors": {"customColors": {"primary": "#FF0000", "secondary": "#00FF00"}, "description": "Update only the color scheme"}, "update_svg": {"customSvg": "<svg width='150' height='150'><circle cx='75' cy='75' r='50' fill='#FF5733'/><text x='75' y='80' text-anchor='middle' fill='white'>Updated</text></svg>", "description": "Update the custom SVG"}, "activate_card": {"active": true, "description": "Activate the card"}, "deactivate_card": {"active": false, "description": "Deactivate the card"}}}, "sample_activation": {"description": "Sample JSON for activating/deactivating a card", "endpoint": "PATCH /api/user-cards/:id/activate", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "application/json"}, "examples": {"activate": {"active": true, "description": "Activate the card"}, "deactivate": {"active": false, "description": "Deactivate the card"}}}, "query_parameters": {"list_cards": {"description": "Query parameters for listing user cards", "endpoint": "GET /api/user-cards", "parameters": {"page": "number (default: 1)", "limit": "number (default: 20, max: 100)", "active": "boolean (filter by active status)", "templateId": "string (filter by template)", "profileId": "string (filter by profile)", "cardType": "string (filter by card type)", "includeDeleted": "boolean (include soft-deleted cards)"}, "examples": {"basic_list": "GET /api/user-cards", "paginated_list": "GET /api/user-cards?page=1&limit=10", "active_cards": "GET /api/user-cards?active=true", "template_filter": "GET /api/user-cards?templateId=template_id_here", "profile_filter": "GET /api/user-cards?profileId=profile_id_here", "include_deleted": "GET /api/user-cards?includeDeleted=true"}}, "search_cards": {"description": "Query parameters for searching user cards", "endpoint": "GET /api/user-cards/search", "parameters": {"q": "string (required, search query)", "limit": "number (default: 20)"}, "examples": {"search_by_text": "GET /api/user-cards/search?q=search_term&limit=10"}}}, "file_upload": {"description": "Upload a QR code image file for the card", "endpoint": "POST /api/user-cards/:id/upload-qr", "headers": {"Authorization": "Bearer YOUR_JWT_TOKEN", "Content-Type": "multipart/form-data"}, "body": {"qrCodeFile": "file (required, QR code image file)"}}, "response_examples": {"success_response": {"success": true, "data": {"_id": "card_id_here", "userId": "user_id_here", "profileId": "profile_id_here", "templateId": "template_id_here", "qrCodeUrl": "https://example.com/qr-code", "nfcData": "nfc_data_here", "customColors": {"primary": "#FF5733", "secondary": "#33FF57"}, "customSvg": "<svg>...</svg>", "active": true, "deleted": false, "createdAt": "2024-01-01T00:00:00.000Z", "updatedAt": "2024-01-01T00:00:00.000Z"}, "message": "User card created successfully"}, "list_response": {"success": true, "data": [{"_id": "card_id_here", "userId": "user_id_here", "profileId": "profile_id_here", "templateId": "template_id_here", "active": true, "createdAt": "2024-01-01T00:00:00.000Z"}], "pagination": {"page": 1, "limit": 20, "total": 5, "pages": 1}}, "stats_response": {"success": true, "data": {"totalCards": 10, "activeCards": 8, "inactiveCards": 2, "cardsWithQr": 5, "cardsWithNfc": 3}}}, "required_fields": {"description": "Required fields for creating a user card", "fields": {"templateId": "string (required) - ID of the card template to use", "profileId": "string (required) - ID of the user profile to associate with the card"}, "optional_fields": {"customColors": "object - Custom color scheme for the card", "customSvg": "string - Custom SVG graphics for the card", "qrCodeUrl": "string - URL of QR code image", "nfcData": "string - NFC data for contactless functionality"}}, "curl_examples": {"create_basic_card": "curl -X POST /api/user-cards -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"templateId\":\"template_id_here\",\"profileId\":\"profile_id_here\"}'", "create_customized_card": "curl -X POST /api/user-cards -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"templateId\":\"template_id_here\",\"profileId\":\"profile_id_here\",\"customColors\":{\"primary\":\"#FF5733\",\"secondary\":\"#33FF57\"},\"customSvg\":\"<svg width=\\\"100\\\" height=\\\"100\\\"><circle cx=\\\"50\\\" cy=\\\"50\\\" r=\\\"40\\\" fill=\\\"#FF5733\\\"/></svg>\",\"qrCodeUrl\":\"https://example.com/qr-code\",\"nfcData\":\"nfc_data_here\"}'", "list_cards": "curl -X GET '/api/user-cards?page=1&limit=10&active=true' -H 'Authorization: Bearer YOUR_TOKEN'", "update_card": "curl -X PUT /api/user-cards/card_id_here -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"customColors\":{\"primary\":\"#FF0000\"},\"active\":true}'", "activate_card": "curl -X PATCH /api/user-cards/card_id_here/activate -H 'Authorization: Bearer YOUR_TOKEN' -H 'Content-Type: application/json' -d '{\"active\":true}'", "upload_qr_code": "curl -X POST /api/user-cards/card_id_here/upload-qr -H 'Authorization: Bearer YOUR_TOKEN' -F 'qrCodeFile=@qr-code.png'"}}