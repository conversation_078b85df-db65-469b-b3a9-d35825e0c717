{"authentication": {"login": {"email": "<EMAIL>", "password": "password123"}, "register": {"email": "<EMAIL>", "password": "password123", "fullName": "<PERSON>", "username": "johndoe", "accountType": "personal", "dateOfBirth": "1990-01-15", "phoneNumber": "+**********", "countryOfResidence": "US", "verificationMethod": "email", "accountCategory": "standard"}}, "products": {"createProductTemplate": {"name": "MyCard Premium Template", "productType": "MyCard", "metadata": {"description": "Premium smart business card template with NFC and QR code", "shortDescription": "Smart business card template", "features": ["NFC enabled", "QR code", "Custom design", "Waterproof"], "specifications": {"dimensions": {"length": 85.6, "width": 53.98, "height": 0.76, "unit": "mm"}, "weight": {"value": 5, "unit": "g"}, "materials": ["PVC", "NFC Chip"], "colors": ["Black", "White", "Blue", "Red"]}, "tags": ["business", "networking", "smart", "nfc"]}, "customization": {"available": true, "options": [{"type": "text", "name": "fullName", "label": "Full Name", "required": true, "maxLength": 50, "additionalCost": 0}, {"type": "text", "name": "jobTitle", "label": "Job Title", "required": false, "maxLength": 30, "additionalCost": 0}, {"type": "color", "name": "cardColor", "label": "Card Color", "required": true, "options": ["Black", "White", "Blue", "Red"], "additionalCost": 0}]}, "digitalFeatures": {"qrCode": {"enabled": true, "customization": {"colors": {"foreground": "#000000", "background": "#FFFFFF"}, "style": "rounded"}}, "nfc": {"enabled": true, "chipType": "NTAG213"}}, "pricing": {"baseCost": 5.0, "materialCost": 2.0, "laborCost": 3.0, "basePrice": 25.0, "currency": "USD"}, "status": "Active", "isTemplate": true}, "updateProduct": {"pricing": {"basePrice": 29.99}, "availability": {"stockLevel": 950}}}, "batches": {"createBatch": {"batchNumber": "MYC-************", "productTemplateId": "60f7b3b3b3b3b3b3b3b3b3b3", "manufacturer": {"bid": "MFG-001", "name": "Premium Card Manufacturing Co.", "contactEmail": "<EMAIL>", "contactPhone": "******-0123"}, "countryOfOrigin": "USA", "category": "Smart Cards", "variants": {"color": "Black", "style": ["Premium", "<PERSON><PERSON>ish"], "size": "Standard"}, "quantity": {"amount": 1000, "nc": 5000.0, "lc": 500.0}, "productInfo": {"name": "MyCard Premium Black", "material": "PVC with NFC Chip", "productType": "mycard"}, "manufacturing": {"estimatedCompletionDate": "2024-02-15T00:00:00Z"}, "mockupImage": "https://cdn.myprofile.com/mockups/mycard-black-premium.jpg", "visibility": "hidden", "autoGenerateProducts": true, "linkFormat": "https://getmyprofile.online/mycard/{uniqueId}", "serialNumberPrefix": "MYC-001-", "startingNumber": 1}, "updateBatchStatus": {"status": "sent_to_manufacturer", "manufacturing": {"sentToManufacturer": true, "sentToManufacturerAt": "2024-01-15T10:00:00Z"}}, "updateManufacturingProgress": {"status": "in_progress", "manufacturing": {"productionStarted": true, "productionStartedAt": "2024-01-16T08:00:00Z", "completedQuantity": 250, "qualityControlPassed": 245, "defectiveItems": 5}}, "markBatchCompleted": {"status": "completed", "manufacturing": {"productionCompleted": true, "productionCompletedAt": "2024-02-10T17:00:00Z", "actualCompletionDate": "2024-02-10T17:00:00Z"}, "compliance": {"quantityCheck": 1000, "qualityControlPassed": 995, "defectiveItems": 5}}}, "orders": {"createOrder": {"customerId": "60f7b3b3b3b3b3b3b3b3b3b3", "customerEmail": "<EMAIL>", "items": [{"productTemplateId": "60f7b3b3b3b3b3b3b3b3b3b3", "name": "MyCard Premium", "quantity": 2, "unitPrice": 25.0, "totalPrice": 50.0, "customization": {"options": [{"type": "fullName", "value": "<PERSON>", "additionalCost": 0}, {"type": "jobTitle", "value": "Software Engineer", "additionalCost": 0}, {"type": "cardColor", "value": "Black", "additionalCost": 0}], "instructions": "Please ensure high quality printing", "approvalStatus": "pending"}, "digitalFeatures": {"qrCode": {"data": "https://getmyprofile.online/mycard/{uniqueId}", "customization": {"colors": {"foreground": "#000000", "background": "#FFFFFF"}}}, "nfc": {"data": "https://getmyprofile.online/mycard/{uniqueId}", "chipType": "NTAG213"}}, "manufacturing": {"batchId": "60f7b3b3b3b3b3b3b3b3b3b3", "requiresCustomProduction": true}}], "pricing": {"subtotal": 50.0, "customizationCosts": 0, "shippingCost": 5.99, "taxes": 4.5, "discounts": 0, "totalAmount": 60.49, "currency": "USD"}, "shipping": {"address": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "street1": "123 Main St", "city": "New York", "state": "NY", "country": "US", "zipCode": "10001", "phone": "+**********"}, "method": "standard", "cost": 5.99}, "payment": {"method": "credit_card", "amount": 60.49, "currency": "USD"}, "workflow": {"currentStage": "pending", "stages": [{"name": "pending", "status": "in_progress"}, {"name": "confirmed", "status": "pending"}, {"name": "manufacturing", "status": "pending"}, {"name": "quality_check", "status": "pending"}, {"name": "packaging", "status": "pending"}, {"name": "shipped", "status": "pending"}]}}, "updateOrderStatus": {"status": "confirmed"}, "assignProductsFromStock": {"orderId": "60f7b3b3b3b3b3b3b3b3b3b3", "productType": "mycard", "quantity": 2, "batchPreference": "60f7b3b3b3b3b3b3b3b3b3b3"}}, "productActivation": {"activateProduct": {"uniqueId": "MYC-001-001-ABC123", "profileId": "60f7b3b3b3b3b3b3b3b3b3b3", "activationCode": "ACT-123456", "customerInfo": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "email": "<EMAIL>", "phone": "+**********"}, "deviceInfo": {"userAgent": "Mozilla/5.0...", "ipAddress": "***********", "location": {"country": "US", "city": "New York"}}}, "updateScanAnalytics": {"uniqueId": "MYC-001-001-ABC123", "scanType": "qr", "location": {"country": "US", "city": "New York", "coordinates": {"latitude": 40.7128, "longitude": -74.006}}, "deviceInfo": {"type": "mobile", "os": "iOS", "browser": "Safari"}, "timestamp": "2024-01-15T10:30:00Z"}, "assignProductToProfile": {"uniqueId": "MYC-001-001-ABC123", "profileId": "60f7b3b3b3b3b3b3b3b3b3b3", "orderId": "60f7b3b3b3b3b3b3b3b3b3b4", "activationData": {"activatedBy": "60f7b3b3b3b3b3b3b3b3b3b3", "activatedAt": "2024-01-15T10:30:00Z", "deviceInfo": {"userAgent": "Mozilla/5.0...", "ipAddress": "***********"}}}}, "inventory": {"updateStockLevel": {"type": "in", "quantity": 100, "reason": "New stock received", "reference": "PO-2024-001"}, "reserveStock": {"productId": "60f7b3b3b3b3b3b3b3b3b3b3", "quantity": 5, "orderId": "60f7b3b3b3b3b3b3b3b3b3b3", "reservationExpiry": "2024-01-16T10:30:00Z"}}, "reviews": {"createReview": {"productId": "60f7b3b3b3b3b3b3b3b3b3b3", "orderId": "60f7b3b3b3b3b3b3b3b3b3b4", "rating": 5, "title": "Excellent quality!", "content": "The MyCard is exactly what I needed. Great quality and fast delivery.", "isVerifiedPurchase": true, "customerInfo": {"displayName": "<PERSON>", "isAnonymous": false}, "media": [{"type": "image", "url": "https://cdn.myprofile.com/reviews/review-image-1.jpg", "caption": "Product in use"}]}, "addHelpfulVote": {"isHelpful": true}, "addBusinessResponse": {"message": "Thank you for your feedback! We're glad you're happy with your MyCard.", "isPublic": true}}, "promotions": {"createPromotion": {"name": "Summer Sale 2024", "description": "20% off all MyCard products", "code": "SUMMER20", "type": "coupon", "discountRules": [{"type": "percentage", "value": 20, "maxDiscount": 50, "minQuantity": 1}], "conditions": [{"type": "min_order_value", "operator": "greater_than", "value": 25.0}, {"type": "product_category", "operator": "in", "values": ["Smart Cards", "Premium"]}], "applicableItems": {"type": "categories", "categoryIds": ["60f7b3b3b3b3b3b3b3b3b3b3"]}, "validity": {"startDate": "2024-06-01T00:00:00Z", "endDate": "2024-08-31T23:59:59Z", "isActive": true}, "usageLimits": {"totalUses": 1000, "usesPerCustomer": 1}, "display": {"showOnStorefront": true, "badgeText": "20% OFF", "badgeColor": "#FF6B6B"}}, "validatePromotion": {"code": "SUMMER20", "customerId": "60f7b3b3b3b3b3b3b3b3b3b3", "orderData": {"subtotal": 50.0, "items": [{"productId": "60f7b3b3b3b3b3b3b3b3b3b3", "quantity": 2, "price": 25.0}]}}}, "shipping": {"createShipping": {"orderId": "60f7b3b3b3b3b3b3b3b3b3b3", "customerId": "60f7b3b3b3b3b3b3b3b3b3b3", "shippingAddress": {"type": "shipping", "firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "street1": "123 Main St", "city": "New York", "state": "NY", "country": "US", "zipCode": "10001", "phone": "+**********"}, "packages": [{"packageId": "PKG-001", "dimensions": {"length": 10, "width": 7, "height": 1, "weight": 0.05, "unit": {"dimensions": "cm", "weight": "kg"}}, "items": [{"productId": "60f7b3b3b3b3b3b3b3b3b3b3", "quantity": 2}], "value": 50.0, "currency": "USD"}], "shippingMethod": {"carrier": "UPS", "service": "Ground", "rate": 5.99, "currency": "USD", "estimatedDays": {"min": 3, "max": 5}}, "costs": {"shippingCost": 5.99, "insuranceCost": 0, "handlingFee": 0, "taxes": 0, "totalCost": 5.99, "currency": "USD"}}, "updateTrackingEvent": {"status": "in_transit", "description": "Package is in transit", "location": {"city": "Philadelphia", "state": "PA", "country": "US", "facility": "UPS Distribution Center"}, "details": "Package departed facility"}, "markAsDelivered": {"deliveredTo": "<PERSON>", "proofType": "signature", "proofData": "signature_image_url", "deliveryNotes": "Left at front door"}}, "analytics": {"updateProductAnalytics": {"productId": "60f7b3b3b3b3b3b3b3b3b3b3", "eventType": "view", "metadata": {"source": "website", "deviceType": "mobile", "location": {"country": "US", "city": "New York"}}}, "recordDigitalEngagement": {"productId": "60f7b3b3b3b3b3b3b3b3b3b3", "engagementType": "qr_scan", "location": {"country": "US", "city": "New York", "coordinates": {"latitude": 40.7128, "longitude": -74.006}}, "deviceInfo": {"type": "mobile", "os": "iOS", "browser": "Safari"}, "timestamp": "2024-01-15T10:30:00Z"}}, "businessAnalytics": {"generateReport": {"period": "monthly", "startDate": "2024-01-01", "endDate": "2024-12-31", "metrics": ["revenue", "orders", "customers", "digital_engagement", "manufacturing_efficiency"], "groupBy": ["productType", "month"], "filters": {"productType": ["mycard", "mytag"], "status": ["completed", "delivered"]}}}, "profiles": {"createProfile": {"userId": "60f7b3b3b3b3b3b3b3b3b3b3", "profileData": {"firstName": "<PERSON>", "lastName": "<PERSON><PERSON>", "jobTitle": "Software Engineer", "company": "Tech Corp", "email": "<EMAIL>", "phone": "+**********", "website": "https://johndoe.com"}, "socialLinks": [{"platform": "linkedin", "url": "https://linkedin.com/in/johndoe"}, {"platform": "twitter", "url": "https://twitter.com/johndoe"}], "customization": {"theme": "professional", "colors": {"primary": "#2563eb", "secondary": "#64748b"}}}, "updateProfile": {"profileData": {"jobTitle": "Senior Software Engineer", "company": "New Tech Corp"}}}, "users": {"updateUser": {"fullName": "<PERSON>", "phoneNumber": "+1234567891", "preferences": {"notifications": {"email": true, "sms": false, "push": true}, "privacy": {"profileVisibility": "public", "analyticsTracking": true}}}}}