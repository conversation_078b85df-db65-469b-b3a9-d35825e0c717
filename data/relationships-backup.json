{"timestamp": "2025-07-24T15:26:24.809Z", "totalCount": 85, "relationshipTypeUsage": {"688230a55abef7df2b02c341": ["688230a65abef7df2b02c396"], "688230a55abef7df2b02c342": ["688230a65abef7df2b02c398"], "688230a55abef7df2b02c343": ["688230a65abef7df2b02c39a"], "688230a55abef7df2b02c344": ["688230a65abef7df2b02c39c"], "688230a55abef7df2b02c345": ["688230a65abef7df2b02c39e"], "688230a55abef7df2b02c346": ["688230a65abef7df2b02c3a0"], "688230a55abef7df2b02c347": ["688230a65abef7df2b02c3a2"], "688230a55abef7df2b02c348": ["688230a65abef7df2b02c3a4"], "688230a55abef7df2b02c349": ["688230a65abef7df2b02c3a6"], "688230a55abef7df2b02c34a": ["688230a65abef7df2b02c3a8"], "688230a55abef7df2b02c34b": ["688230a65abef7df2b02c3aa"], "688230a55abef7df2b02c34c": ["688230a65abef7df2b02c3ac"], "688230a55abef7df2b02c34d": ["688230a65abef7df2b02c3ae"], "688230a55abef7df2b02c34e": ["688230a65abef7df2b02c3b0"], "688230a55abef7df2b02c34f": ["688230a65abef7df2b02c3b2"], "688230a55abef7df2b02c350": ["688230a65abef7df2b02c3b4"], "688230a55abef7df2b02c351": ["688230a65abef7df2b02c3b6"], "688230a55abef7df2b02c352": ["688230a65abef7df2b02c3b8"], "688230a55abef7df2b02c353": ["688230a65abef7df2b02c3ba"], "688230a55abef7df2b02c354": ["688230a65abef7df2b02c3bc"], "688230a55abef7df2b02c355": ["688230a65abef7df2b02c3be"], "688230a55abef7df2b02c356": ["688230a65abef7df2b02c3c0"], "688230a55abef7df2b02c357": ["688230a65abef7df2b02c3c2"], "688230a55abef7df2b02c358": ["688230a65abef7df2b02c3c4"], "688230a55abef7df2b02c359": ["688230a65abef7df2b02c3c6"], "688230a55abef7df2b02c35a": ["688230a65abef7df2b02c3c8"], "688230a55abef7df2b02c35b": ["688230a65abef7df2b02c3ca"], "688230a55abef7df2b02c35c": ["688230a65abef7df2b02c3cc"], "688230a55abef7df2b02c35d": ["688230a65abef7df2b02c3ce"], "688230a55abef7df2b02c35e": ["688230a65abef7df2b02c3d0"], "688230a55abef7df2b02c35f": ["688230a65abef7df2b02c3d2"], "688230a55abef7df2b02c360": ["688230a65abef7df2b02c3d4"], "688230a55abef7df2b02c361": ["688230a65abef7df2b02c3d6"], "688230a55abef7df2b02c362": ["688230a65abef7df2b02c3d8"], "688230a55abef7df2b02c363": ["688230a65abef7df2b02c3da"], "688230a55abef7df2b02c364": ["688230a65abef7df2b02c3dc"], "688230a55abef7df2b02c365": ["688230a65abef7df2b02c3de"], "688230a55abef7df2b02c366": ["688230a65abef7df2b02c3e0"], "688230a55abef7df2b02c367": ["688230a65abef7df2b02c3e2"], "688230a55abef7df2b02c368": ["688230a75abef7df2b02c3e4"], "688230a55abef7df2b02c369": ["688230a75abef7df2b02c3e6"], "688230a55abef7df2b02c36a": ["688230a75abef7df2b02c3e8"], "688230a55abef7df2b02c36b": ["688230a75abef7df2b02c3ea"], "688230a55abef7df2b02c36c": ["688230a75abef7df2b02c3ec"], "688230a55abef7df2b02c36d": ["688230a75abef7df2b02c3ee"], "688230a55abef7df2b02c36e": ["688230a75abef7df2b02c3f0"], "688230a55abef7df2b02c36f": ["688230a75abef7df2b02c3f2"], "688230a55abef7df2b02c370": ["688230a75abef7df2b02c3f4"], "688230a55abef7df2b02c371": ["688230a75abef7df2b02c3f6"], "688230a55abef7df2b02c372": ["688230a75abef7df2b02c3f8"], "688230a55abef7df2b02c373": ["688230a75abef7df2b02c3fa"], "688230a55abef7df2b02c374": ["688230a75abef7df2b02c3fc"], "688230a55abef7df2b02c375": ["688230a75abef7df2b02c3fe"], "688230a55abef7df2b02c376": ["688230a75abef7df2b02c400"], "688230a55abef7df2b02c377": ["688230a75abef7df2b02c402"], "688230a55abef7df2b02c378": ["688230a75abef7df2b02c404"], "688230a55abef7df2b02c379": ["688230a75abef7df2b02c406"], "688230a55abef7df2b02c37a": ["688230a75abef7df2b02c408"], "688230a55abef7df2b02c37b": ["688230a75abef7df2b02c40a"], "688230a55abef7df2b02c37c": ["688230a75abef7df2b02c40c"], "688230a55abef7df2b02c37d": ["688230a75abef7df2b02c40e"], "688230a55abef7df2b02c37e": ["688230a75abef7df2b02c410"], "688230a55abef7df2b02c37f": ["688230a75abef7df2b02c412"], "688230a55abef7df2b02c380": ["688230a75abef7df2b02c414"], "688230a55abef7df2b02c381": ["688230a75abef7df2b02c416"], "688230a55abef7df2b02c382": ["688230a75abef7df2b02c418"], "688230a55abef7df2b02c383": ["688230a75abef7df2b02c41a"], "688230a55abef7df2b02c384": ["688230a75abef7df2b02c41c"], "688230a55abef7df2b02c385": ["688230a75abef7df2b02c41e"], "688230a55abef7df2b02c386": ["688230a75abef7df2b02c420"], "688230a55abef7df2b02c387": ["688230a75abef7df2b02c422"], "688230a55abef7df2b02c388": ["688230a75abef7df2b02c424"], "688230a55abef7df2b02c389": ["688230a75abef7df2b02c426"], "688230a55abef7df2b02c38a": ["688230a75abef7df2b02c428"], "688230a55abef7df2b02c38b": ["688230a75abef7df2b02c42a"], "688230a55abef7df2b02c38c": ["688230a75abef7df2b02c42c"], "688230a55abef7df2b02c38d": ["688230a75abef7df2b02c42e"], "688230a55abef7df2b02c38e": ["688230a75abef7df2b02c430"], "688230a55abef7df2b02c38f": ["688230a75abef7df2b02c432"], "688230a55abef7df2b02c390": ["688230a75abef7df2b02c434"], "688230a55abef7df2b02c391": ["688230a75abef7df2b02c436"], "688230a55abef7df2b02c392": ["688230a75abef7df2b02c438"], "688230a55abef7df2b02c393": ["688230a75abef7df2b02c43a"]}, "invalidReferences": [{"relationshipId": "68820aecfef57132445a33a1", "relationshipTypeId": "687fffb3cbfb39611d7ee794", "index": 0}, {"relationshipId": "68820bb3fef57132445a33cf", "relationshipTypeId": "687fffb3cbfb39611d7ee792", "index": 1}], "data": [{"_id": "68820aecfef57132445a33a1", "relationshipTypeId": "687fffb3cbfb39611d7ee794", "relationshipName": "asasfsafssfsfafsfsg", "inverseRelationshipName": "Unknown", "note": "sasfaasf", "status": "pending", "visibility": "private", "tags": ["safsaf", "asfsaf"], "createdBy": "6867737f8274523b7db7795a", "createdAt": "2025-07-24T10:29:00.232Z", "__v": 0, "updatedAt": "2025-07-24T14:40:45.920Z"}, {"_id": "68820bb3fef57132445a33cf", "relationshipTypeId": "687fffb3cbfb39611d7ee792", "relationshipName": "old Brother", "inverseRelationshipName": "Unknown", "note": "asfsafsfa", "status": "pending", "visibility": "private", "tags": ["familyss"], "createdBy": "6867737f8274523b7db7795a", "createdAt": "2025-07-24T10:32:19.934Z", "__v": 0, "updatedAt": "2025-07-24T14:40:47.973Z"}, {"_id": "688230a65abef7df2b02c396", "relationshipTypeId": "688230a55abef7df2b02c341", "relationshipName": "Adopted Sister", "inverseRelationshipName": "Adopted Sister", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "adopted"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.954Z", "updatedAt": "2025-07-24T14:40:48.289Z", "__v": 0}, {"_id": "688230a65abef7df2b02c398", "relationshipTypeId": "688230a55abef7df2b02c342", "relationshipName": "Family", "inverseRelationshipName": "Family", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.954Z", "updatedAt": "2025-07-24T14:40:48.916Z", "__v": 0}, {"_id": "688230a65abef7df2b02c39a", "relationshipTypeId": "688230a55abef7df2b02c343", "relationshipName": "Acquaintance", "inverseRelationshipName": "Acquaintance", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.954Z", "updatedAt": "2025-07-24T14:40:49.703Z", "__v": 0}, {"_id": "688230a65abef7df2b02c39c", "relationshipTypeId": "688230a55abef7df2b02c344", "relationshipName": "Best friend", "inverseRelationshipName": "Best friend", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social", "friendship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.954Z", "updatedAt": "2025-07-24T14:40:50.522Z", "__v": 0}, {"_id": "688230a65abef7df2b02c39e", "relationshipTypeId": "688230a55abef7df2b02c345", "relationshipName": "Boyfriend", "inverseRelationshipName": "Girlfriend", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social", "friendship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.954Z", "updatedAt": "2025-07-24T14:40:51.124Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3a0", "relationshipTypeId": "688230a55abef7df2b02c346", "relationshipName": "Girlfriend", "inverseRelationshipName": "Boyfriend", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social", "friendship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:51.753Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3a2", "relationshipTypeId": "688230a55abef7df2b02c347", "relationshipName": "Casual friend", "inverseRelationshipName": "Casual friend", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social", "friendship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:52.373Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3a4", "relationshipTypeId": "688230a55abef7df2b02c348", "relationshipName": "Close friend", "inverseRelationshipName": "Close friend", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social", "friendship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:52.972Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3a6", "relationshipTypeId": "688230a55abef7df2b02c349", "relationshipName": "<PERSON><PERSON><PERSON>", "inverseRelationshipName": "Fiancée", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:53.594Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3a8", "relationshipTypeId": "688230a55abef7df2b02c34a", "relationshipName": "Fiancée", "inverseRelationshipName": "<PERSON><PERSON><PERSON>", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:54.314Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3aa", "relationshipTypeId": "688230a55abef7df2b02c34b", "relationshipName": "Mentee", "inverseRelationshipName": "Mentor", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:54.926Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3ac", "relationshipTypeId": "688230a55abef7df2b02c34c", "relationshipName": "Mentor", "inverseRelationshipName": "Mentee", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:55.652Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3ae", "relationshipTypeId": "688230a55abef7df2b02c34d", "relationshipName": "Neighbor", "inverseRelationshipName": "Neighbor", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:56.359Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3b0", "relationshipTypeId": "688230a55abef7df2b02c34e", "relationshipName": "Partner", "inverseRelationshipName": "Partner", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:56.974Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3b2", "relationshipTypeId": "688230a55abef7df2b02c34f", "relationshipName": "Teammate", "inverseRelationshipName": "Teammate", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": ["personal", "social"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:57.690Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3b4", "relationshipTypeId": "688230a55abef7df2b02c350", "relationshipName": "Adopted brother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "adopted"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:58.407Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3b6", "relationshipTypeId": "688230a55abef7df2b02c351", "relationshipName": "Adopted daughter", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "adopted"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:59.226Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3b8", "relationshipTypeId": "688230a55abef7df2b02c352", "relationshipName": "Adopted son", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "adopted"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:40:59.935Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3ba", "relationshipTypeId": "688230a55abef7df2b02c353", "relationshipName": "Adopted father", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "adopted"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:00.702Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3bc", "relationshipTypeId": "688230a55abef7df2b02c354", "relationshipName": "Adopted mother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "adopted"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:01.376Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3be", "relationshipTypeId": "688230a55abef7df2b02c355", "relationshipName": "Aunt", "inverseRelationshipName": "Ni<PERSON>e/Nephew", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:02.150Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3c0", "relationshipTypeId": "688230a55abef7df2b02c356", "relationshipName": "Uncle", "inverseRelationshipName": "Ni<PERSON>e/Nephew", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:02.752Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3c2", "relationshipTypeId": "688230a55abef7df2b02c357", "relationshipName": "Biological brother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "biological"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:03.424Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3c4", "relationshipTypeId": "688230a55abef7df2b02c358", "relationshipName": "Biological sister", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "biological"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:04.141Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3c6", "relationshipTypeId": "688230a55abef7df2b02c359", "relationshipName": "Biological daughter", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "biological"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:04.756Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3c8", "relationshipTypeId": "688230a55abef7df2b02c35a", "relationshipName": "Biological son", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "biological"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.955Z", "updatedAt": "2025-07-24T14:41:05.575Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3ca", "relationshipTypeId": "688230a55abef7df2b02c35b", "relationshipName": "Biological father", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "biological"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:06.395Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3cc", "relationshipTypeId": "688230a55abef7df2b02c35c", "relationshipName": "Biological mother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "biological"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:07.111Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3ce", "relationshipTypeId": "688230a55abef7df2b02c35d", "relationshipName": "<PERSON><PERSON><PERSON> (First)", "inverseRelationshipName": "<PERSON><PERSON><PERSON> (First)", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:07.932Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3d0", "relationshipTypeId": "688230a55abef7df2b02c35e", "relationshipName": "<PERSON><PERSON><PERSON> (Second)", "inverseRelationshipName": "<PERSON><PERSON><PERSON> (Second)", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:08.954Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3d2", "relationshipTypeId": "688230a55abef7df2b02c35f", "relationshipName": "<PERSON><PERSON><PERSON> (Third)", "inverseRelationshipName": "<PERSON><PERSON><PERSON> (Third)", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:09.976Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3d4", "relationshipTypeId": "688230a55abef7df2b02c360", "relationshipName": "Foster brother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:11.007Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3d6", "relationshipTypeId": "688230a55abef7df2b02c361", "relationshipName": "Foster sister", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:11.821Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3d8", "relationshipTypeId": "688230a55abef7df2b02c362", "relationshipName": "Foster daughter", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:12.582Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3da", "relationshipTypeId": "688230a55abef7df2b02c363", "relationshipName": "Foster son", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:13.669Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3dc", "relationshipTypeId": "688230a55abef7df2b02c364", "relationshipName": "Foster father", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:14.486Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3de", "relationshipTypeId": "688230a55abef7df2b02c365", "relationshipName": "Foster mother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:15.509Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3e0", "relationshipTypeId": "688230a55abef7df2b02c366", "relationshipName": "Granddaughter", "inverseRelationshipName": "Grandparent", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:16.413Z", "__v": 0}, {"_id": "688230a65abef7df2b02c3e2", "relationshipTypeId": "688230a55abef7df2b02c367", "relationshipName": "<PERSON><PERSON>", "inverseRelationshipName": "Grandparent", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:17.161Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3e4", "relationshipTypeId": "688230a55abef7df2b02c368", "relationshipName": "Grandfather", "inverseRelationshipName": "Grandchild", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:17.966Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3e6", "relationshipTypeId": "688230a55abef7df2b02c369", "relationshipName": "Grandmother", "inverseRelationshipName": "Grandchild", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:18.785Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3e8", "relationshipTypeId": "688230a55abef7df2b02c36a", "relationshipName": "Husband", "inverseRelationshipName": "Wife", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:19.603Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3ea", "relationshipTypeId": "688230a55abef7df2b02c36b", "relationshipName": "Wife", "inverseRelationshipName": "Husband", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:20.424Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3ec", "relationshipTypeId": "688230a55abef7df2b02c36c", "relationshipName": "<PERSON><PERSON><PERSON><PERSON>", "inverseRelationshipName": "Aunt/Uncle", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:21.127Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3ee", "relationshipTypeId": "688230a55abef7df2b02c36d", "relationshipName": "<PERSON><PERSON><PERSON>", "inverseRelationshipName": "Aunt/Uncle", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:22.062Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3f0", "relationshipTypeId": "688230a55abef7df2b02c36e", "relationshipName": "<PERSON><PERSON><PERSON>", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "step-family"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:22.697Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3f2", "relationshipTypeId": "688230a55abef7df2b02c36f", "relationshipName": "Stepsister", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "step-family"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:23.495Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3f4", "relationshipTypeId": "688230a55abef7df2b02c370", "relationshipName": "Stepdaughter", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "step-family"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:24.215Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3f6", "relationshipTypeId": "688230a55abef7df2b02c371", "relationshipName": "<PERSON><PERSON>", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "step-family"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:25.134Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3f8", "relationshipTypeId": "688230a55abef7df2b02c372", "relationshipName": "Stepfather", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "step-family"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:25.953Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3fa", "relationshipTypeId": "688230a55abef7df2b02c373", "relationshipName": "Stepmother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship", "step-family"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:26.671Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3fc", "relationshipTypeId": "688230a55abef7df2b02c374", "relationshipName": "Twin brother", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:27.386Z", "__v": 0}, {"_id": "688230a75abef7df2b02c3fe", "relationshipTypeId": "688230a55abef7df2b02c375", "relationshipName": "Twin sister", "inverseRelationshipName": "Unknown", "note": "Sample relationship created from Family data", "status": "approved", "visibility": "public", "tags": ["family", "kinship"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:28.103Z", "__v": 0}, {"_id": "688230a75abef7df2b02c400", "relationshipTypeId": "688230a55abef7df2b02c376", "relationshipName": "Affiliate", "inverseRelationshipName": "Affiliate", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:28.921Z", "__v": 0}, {"_id": "688230a75abef7df2b02c402", "relationshipTypeId": "688230a55abef7df2b02c377", "relationshipName": "Advisor (Financial)", "inverseRelationshipName": "Client", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:29.640Z", "__v": 0}, {"_id": "688230a75abef7df2b02c404", "relationshipTypeId": "688230a55abef7df2b02c378", "relationshipName": "Advisor (Legal)", "inverseRelationshipName": "Client", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:30.357Z", "__v": 0}, {"_id": "688230a75abef7df2b02c406", "relationshipTypeId": "688230a55abef7df2b02c379", "relationshipName": "Association", "inverseRelationshipName": "Association", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:31.073Z", "__v": 0}, {"_id": "688230a75abef7df2b02c408", "relationshipTypeId": "688230a55abef7df2b02c37a", "relationshipName": "Auditor", "inverseRelationshipName": "Auditee", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:31.688Z", "__v": 0}, {"_id": "688230a75abef7df2b02c40a", "relationshipTypeId": "688230a55abef7df2b02c37b", "relationshipName": "Board Member", "inverseRelationshipName": "Organization", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:32.303Z", "__v": 0}, {"_id": "688230a75abef7df2b02c40c", "relationshipTypeId": "688230a55abef7df2b02c37c", "relationshipName": "Client", "inverseRelationshipName": "Advisor/Service Provider", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:32.914Z", "__v": 0}, {"_id": "688230a75abef7df2b02c40e", "relationshipTypeId": "688230a55abef7df2b02c37d", "relationshipName": "Customer", "inverseRelationshipName": "Vendor/Service Provider", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:33.531Z", "__v": 0}, {"_id": "688230a75abef7df2b02c410", "relationshipTypeId": "688230a55abef7df2b02c37e", "relationshipName": "Competitor", "inverseRelationshipName": "Competitor", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:34.190Z", "__v": 0}, {"_id": "688230a75abef7df2b02c412", "relationshipTypeId": "688230a55abef7df2b02c37f", "relationshipName": "Consultant", "inverseRelationshipName": "Client", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:35.025Z", "__v": 0}, {"_id": "688230a75abef7df2b02c414", "relationshipTypeId": "688230a55abef7df2b02c380", "relationshipName": "Contractor", "inverseRelationshipName": "Client", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.956Z", "updatedAt": "2025-07-24T14:41:35.681Z", "__v": 0}, {"_id": "688230a75abef7df2b02c416", "relationshipTypeId": "688230a55abef7df2b02c381", "relationshipName": "Distributor", "inverseRelationshipName": "Manufacturer/Supplier", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:36.501Z", "__v": 0}, {"_id": "688230a75abef7df2b02c418", "relationshipTypeId": "688230a55abef7df2b02c382", "relationshipName": "Franchisee", "inverseRelationshipName": "Franchisor", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:37.097Z", "__v": 0}, {"_id": "688230a75abef7df2b02c41a", "relationshipTypeId": "688230a55abef7df2b02c383", "relationshipName": "Franchisor", "inverseRelationshipName": "Franchisee", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:37.695Z", "__v": 0}, {"_id": "688230a75abef7df2b02c41c", "relationshipTypeId": "688230a55abef7df2b02c384", "relationshipName": "Industry Consortium Member", "inverseRelationshipName": "Industry Consortium Member", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:38.444Z", "__v": 0}, {"_id": "688230a75abef7df2b02c41e", "relationshipTypeId": "688230a55abef7df2b02c385", "relationshipName": "Investor", "inverseRelationshipName": "Company/Startup", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:39.266Z", "__v": 0}, {"_id": "688230a75abef7df2b02c420", "relationshipTypeId": "688230a55abef7df2b02c386", "relationshipName": "Joint Venture Partner", "inverseRelationshipName": "Joint Venture Partner", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:39.880Z", "__v": 0}, {"_id": "688230a75abef7df2b02c422", "relationshipTypeId": "688230a55abef7df2b02c387", "relationshipName": "Licensing Authority", "inverseRelationshipName": "Licensee", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:40.494Z", "__v": 0}, {"_id": "688230a75abef7df2b02c424", "relationshipTypeId": "688230a55abef7df2b02c388", "relationshipName": "Marketing Partner", "inverseRelationshipName": "Marketing Partner", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:41.263Z", "__v": 0}, {"_id": "688230a75abef7df2b02c426", "relationshipTypeId": "688230a55abef7df2b02c389", "relationshipName": "Outsourcing Partner", "inverseRelationshipName": "Client", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:41.929Z", "__v": 0}, {"_id": "688230a75abef7df2b02c428", "relationshipTypeId": "688230a55abef7df2b02c38a", "relationshipName": "Parent Company", "inverseRelationshipName": "Subsidiary", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:42.525Z", "__v": 0}, {"_id": "688230a75abef7df2b02c42a", "relationshipTypeId": "688230a55abef7df2b02c38b", "relationshipName": "Subsidiary", "inverseRelationshipName": "Parent Company", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:43.158Z", "__v": 0}, {"_id": "688230a75abef7df2b02c42c", "relationshipTypeId": "688230a55abef7df2b02c38c", "relationshipName": "Regulatory Body", "inverseRelationshipName": "Regulated Entity", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:43.973Z", "__v": 0}, {"_id": "688230a75abef7df2b02c42e", "relationshipTypeId": "688230a55abef7df2b02c38d", "relationshipName": "Service Provider", "inverseRelationshipName": "Client", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:44.689Z", "__v": 0}, {"_id": "688230a75abef7df2b02c430", "relationshipTypeId": "688230a55abef7df2b02c38e", "relationshipName": "Shareholder", "inverseRelationshipName": "Company", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:45.298Z", "__v": 0}, {"_id": "688230a75abef7df2b02c432", "relationshipTypeId": "688230a55abef7df2b02c38f", "relationshipName": "Strategic Partner", "inverseRelationshipName": "Strategic Partner", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:45.921Z", "__v": 0}, {"_id": "688230a75abef7df2b02c434", "relationshipTypeId": "688230a55abef7df2b02c390", "relationshipName": "Supplier", "inverseRelationshipName": "Customer", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:46.533Z", "__v": 0}, {"_id": "688230a75abef7df2b02c436", "relationshipTypeId": "688230a55abef7df2b02c391", "relationshipName": "Technology Partner", "inverseRelationshipName": "Technology Partner", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:47.147Z", "__v": 0}, {"_id": "688230a75abef7df2b02c438", "relationshipTypeId": "688230a55abef7df2b02c392", "relationshipName": "<PERSON><PERSON><PERSON>", "inverseRelationshipName": "Customer", "note": "Sample relationship created from Professional data", "status": "approved", "visibility": "public", "tags": ["professional", "business"], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:47.797Z", "__v": 0}, {"_id": "688230a75abef7df2b02c43a", "relationshipTypeId": "688230a55abef7df2b02c393", "relationshipName": "Study Buddy", "inverseRelationshipName": "Study Buddy", "note": "Sample relationship created from Personal data", "status": "approved", "visibility": "public", "tags": [], "createdBy": "000000000000000000000000", "createdAt": "2025-07-24T13:09:58.957Z", "updatedAt": "2025-07-24T14:41:48.395Z", "__v": 0}]}