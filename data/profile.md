# Example Community and Group Profiles

## Community Profiles

```json
{
  "profileCategory": "group",
  "profileType": "community",
  "templatedId": "682d5795e699107bb25780f0",
  "profileInformation": {
    "username": "GreenVille Community",
    "title": "GreenVille Residents",
    "profileLink": "greenville-community-link",
    "creator": "USERID1",
    "connectLink": "connect-greenville-123",
    "followLink": "follow-greenville-123",
    "createdAt": "2024-05-21T10:00:00.000Z",
    "updatedAt": "2024-05-21T10:00:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "community_name", "value": "GreenVille Community", "enabled": true },
        { "key": "profile_type", "value": "Community Profile", "enabled": true },
        { "key": "details", "value": "A vibrant neighborhood community.", "enabled": true }
      ]
    },
    {
      "key": "format",
      "label": "Format",
      "fields": [
        { "key": "profile_card_image", "value": "https://example.com/greenville-card.jpg", "enabled": true },
        { "key": "profile_logo", "value": "https://example.com/greenville-logo.png", "enabled": true },
        { "key": "profile_layout", "value": "default", "enabled": true },
        { "key": "profile_card_color", "value": "#4CAF50", "enabled": true }
      ]
    }
  ]
}
```

```json
{
  "profileCategory": "group",
  "profileType": "community",
  "templatedId": "682d5795e699107bb25780f0",
  "profileInformation": {
    "username": "Tech Innovators",
    "title": "Tech Innovators Community",
    "profileLink": "tech-innovators-link",
    "creator": "USERID2",
    "connectLink": "connect-tech-innovators-456",
    "followLink": "follow-tech-innovators-456",
    "createdAt": "2024-05-21T11:00:00.000Z",
    "updatedAt": "2024-05-21T11:00:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "community_name", "value": "Tech Innovators", "enabled": true },
        { "key": "profile_type", "value": "Community Profile", "enabled": true },
        { "key": "details", "value": "A global community for tech enthusiasts.", "enabled": true }
      ]
    },
    {
      "key": "format",
      "label": "Format",
      "fields": [
        { "key": "profile_card_image", "value": "https://example.com/tech-card.jpg", "enabled": true },
        { "key": "profile_logo", "value": "https://example.com/tech-logo.png", "enabled": true },
        { "key": "profile_layout", "value": "layout1", "enabled": true },
        { "key": "profile_card_color", "value": "#2196F3", "enabled": true }
      ]
    }
  ]
}
```

## Group Profiles

```json
{
  "profileCategory": "group",
  "profileType": "group",
  "templatedId": "682d59dae699107bb2578111",
  "profileInformation": {
    "username": "Book Club",
    "title": "Book Club Group",
    "profileLink": "book-club-link",
    "creator": "USERID3",
    "connectLink": "connect-book-club-001",
    "followLink": "follow-book-club-001",
    "createdAt": "2024-05-21T12:00:00.000Z",
    "updatedAt": "2024-05-21T12:00:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "group_profile", "value": "Book Club", "enabled": true },
        { "key": "profile_title", "value": "Book Lovers", "enabled": true },
        { "key": "group_profile_holder", "value": "Alice Smith", "enabled": true },
        { "key": "relationship_to_account", "value": "Founder", "enabled": true },
        { "key": "group_profile_username", "value": "bookclub2024", "enabled": true },
        { "key": "profile_identification_number", "value": "BC-001", "enabled": true }
      ]
    }
  ]
}
```

```json
{
  "profileCategory": "group",
  "profileType": "group",
  "templatedId": "682d59dae699107bb2578111",
  "profileInformation": {
    "username": "Chess Masters",
    "title": "Chess Masters Group",
    "profileLink": "chess-masters-link",
    "creator": "USERID4",
    "connectLink": "connect-chess-masters-002",
    "followLink": "follow-chess-masters-002",
    "createdAt": "2024-05-21T12:30:00.000Z",
    "updatedAt": "2024-05-21T12:30:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "group_profile", "value": "Chess Masters", "enabled": true },
        { "key": "profile_title", "value": "Chess Enthusiasts", "enabled": true },
        { "key": "group_profile_holder", "value": "Bob Lee", "enabled": true },
        { "key": "relationship_to_account", "value": "Admin", "enabled": true },
        { "key": "group_profile_username", "value": "chessmasters", "enabled": true },
        { "key": "profile_identification_number", "value": "CM-002", "enabled": true }
      ]
    }
  ]
}
```

```json
{
  "profileCategory": "group",
  "profileType": "group",
  "templatedId": "682d59dae699107bb2578111",
  "profileInformation": {
    "username": "Hiking Crew",
    "title": "Hiking Crew Group",
    "profileLink": "hiking-crew-link",
    "creator": "USERID5",
    "connectLink": "connect-hiking-crew-003",
    "followLink": "follow-hiking-crew-003",
    "createdAt": "2024-05-21T13:00:00.000Z",
    "updatedAt": "2024-05-21T13:00:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "group_profile", "value": "Hiking Crew", "enabled": true },
        { "key": "profile_title", "value": "Mountain Hikers", "enabled": true },
        { "key": "group_profile_holder", "value": "Carol Danvers", "enabled": true },
        { "key": "relationship_to_account", "value": "Leader", "enabled": true },
        { "key": "group_profile_username", "value": "hikingcrew", "enabled": true },
        { "key": "profile_identification_number", "value": "HC-003", "enabled": true }
      ]
    }
  ]
}
```

```json
{
  "profileCategory": "group",
  "profileType": "group",
  "templatedId": "682d59dae699107bb2578111",
  "profileInformation": {
    "username": "Music Band",
    "title": "Music Band Group",
    "profileLink": "music-band-link",
    "creator": "USERID6",
    "connectLink": "connect-music-band-004",
    "followLink": "follow-music-band-004",
    "createdAt": "2024-05-21T13:30:00.000Z",
    "updatedAt": "2024-05-21T13:30:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "group_profile", "value": "Music Band", "enabled": true },
        { "key": "profile_title", "value": "Band Members", "enabled": true },
        { "key": "group_profile_holder", "value": "David Kim", "enabled": true },
        { "key": "relationship_to_account", "value": "Manager", "enabled": true },
        { "key": "group_profile_username", "value": "musicband", "enabled": true },
        { "key": "profile_identification_number", "value": "MB-004", "enabled": true }
      ]
    }
  ]
}
```

```json
{
  "profileCategory": "group",
  "profileType": "group",
  "templatedId": "682d59dae699107bb2578111",
  "profileInformation": {
    "username": "Art Collective",
    "title": "Art Collective Group",
    "profileLink": "art-collective-link",
    "creator": "USERID7",
    "connectLink": "connect-art-collective-005",
    "followLink": "follow-art-collective-005",
    "createdAt": "2024-05-21T14:00:00.000Z",
    "updatedAt": "2024-05-21T14:00:00.000Z"
  },
  "sections": [
    {
      "key": "info",
      "label": "Info",
      "fields": [
        { "key": "group_profile", "value": "Art Collective", "enabled": true },
        { "key": "profile_title", "value": "Artists United", "enabled": true },
        { "key": "group_profile_holder", "value": "Eva Green", "enabled": true },
        { "key": "relationship_to_account", "value": "Curator", "enabled": true },
        { "key": "group_profile_username", "value": "artcollective", "enabled": true },
        { "key": "profile_identification_number", "value": "AC-005", "enabled": true }
      ]
    }
  ]
}
``` 