Category,HTTP Method,Endpoint Path,Description,Access Level,Authentication Required
Auth,GET,/api/auth/,API Documentation endpoint,Public,No
Auth,GET,/api/auth/healthcheck,Health check endpoint,Public,No
Auth,GET,/api/auth/verify,Verify authentication status,Private,Yes
Auth,POST,/api/auth/register,User registration,Public,No
Auth,POST,/api/auth/login,User login,Public,No
Auth,POST,/api/auth/refresh-token,Refresh access token,Public,No
Auth,POST,/api/auth/logout,User logout,Private,Yes
Auth,POST,/api/auth/logout-all,Logout from all sessions,Private,Yes
Auth,POST,/api/auth/logout-all-sessions,Logout all sessions,Public,No
Auth,POST,/api/auth/trouble-login,Trouble login assistance,Public,No
Auth,GET,/api/auth/sessions,Get user sessions,Private,Yes
Auth,POST,/api/auth/revoke-session,Revoke a particular session,Private,Yes
Auth,DELETE,/api/auth/sessions/:sessionId,Remove specific session,Private,Yes
Auth,POST,/api/auth/forgot-password,Forgot password,Public,No
Auth,POST,/api/auth/reset-password,Reset password,Public,No
Auth,POST,/api/auth/verify,Verify token,Public,No
Auth,GET,/api/auth/check-email/:email,Check email availability,Public,No
Auth,GET,/api/auth/check-username/:username,Check username availability,Public,No
Auth,GET,/api/auth/user/me,Get current user info,Private,Yes
Auth,GET,/api/auth/user/info,Get user info from token,Public,No
Auth,GET,/api/auth/user/:id,Get user by ID,Public,No
Auth,PUT,/api/auth/user/:id,Update user by ID,Public,No
Auth,DELETE,/api/auth/user/:id,Delete user by ID,Public,No
Auth,POST,/api/auth/verify-otp,Verify OTP,Public,No
Auth,POST,/api/auth/resend-otp,Resend OTP,Public,No
Auth,POST,/api/auth/select-otp-method,Select OTP method,Public,No
Auth,POST,/api/auth/change-identifier,Change identifier,Private,Yes
Auth,POST,/api/auth/verify-email,Email verification,Public,No
Auth,POST,/api/auth/resend-verification,Resend verification,Public,No
Auth,POST,/api/auth/resend-verification-email,Resend verification email,Private,Yes
Auth,POST,/api/auth/change-email,Change email,Private,Yes
Auth,POST,/api/auth/change-phone,Change phone number,Private,Yes
Auth,POST,/api/auth/change-username,Change username,Private,Yes
Auth,POST,/api/auth/update-profile,Update profile information,Private,Yes
Auth,POST,/api/auth/2fa/generate,Generate 2FA,Private,Yes
Auth,POST,/api/auth/2fa/verify,Verify 2FA,Private,Yes
Auth,POST,/api/auth/2fa/disable,Disable 2FA,Private,Yes
Auth,POST,/api/auth/2fa/validate,Validate 2FA,Public,No
Auth,GET,/api/auth/google,Google OAuth initiation,Public,No
Auth,GET,/api/auth/google/callback,Google OAuth callback,Public,No
Auth,POST,/api/auth/google/mobile,Google mobile OAuth,Public,No
Auth,GET,/api/auth/facebook,Facebook OAuth initiation,Public,No
Auth,GET,/api/auth/facebook/callback,Facebook OAuth callback,Public,No
Auth,GET,/api/auth/linkedin,LinkedIn OAuth initiation,Public,No
Auth,GET,/api/auth/linkedin/callback,LinkedIn OAuth callback,Public,No
Auth,POST,/api/auth/test-email,Test email sending,Public,No
Auth,POST,/api/auth/test-whatsapp,Test WhatsApp messaging,Public,No
Auth,POST,/api/auth/public/clear-sessions,Clear all user sessions by email,Public,No
Profiles,GET,/api/profiles/all,Get all profiles,Private,Yes
Profiles,GET,/api/profiles/p,Get user profiles,Private,Yes
Profiles,POST,/api/profiles/default,Create default profile,Private,Yes
Profiles,DELETE,/api/profiles/duplicates/personal,Delete duplicate personal profiles,Admin,Yes
Profiles,GET,/api/profiles/communities,Get community profiles with filters,Public,No
Profiles,POST,/api/profiles/t/create,Create profile template,Private,Yes
Profiles,POST,/api/profiles/t/bulk-create,Bulk create profile templates,Private,Yes
Profiles,GET,/api/profiles/t/list,List profile templates,Private,Yes
Profiles,GET,/api/profiles/t/:id,Get profile template by ID,Private,Yes
Profiles,PUT,/api/profiles/t/:id,Update profile template,Private,Yes
Profiles,DELETE,/api/profiles/t/:id,Delete profile template,Private,Yes
Profiles,POST,/api/profiles/p,Create profile,Private,Yes
Profiles,GET,/api/profiles/p/:profileId,Get profile,Public,No
Profiles,PUT,/api/profiles/p/:profileId/content,Update profile content,Private,Yes
Profiles,PUT,/api/profiles/p/:profileId/basic-info,Update profile basic info,Private,Yes
Profiles,DELETE,/api/profiles/p/:profileId,Delete profile,Private,Yes
Profiles,GET,/api/profiles/p/:profileId/structure,Get profile structure,Public,No
Profiles,POST,/api/profiles/p/:profileId/fields,Set enabled fields,Private,Yes
Profiles,POST,/api/profiles/p/:profileId/sync,Sync profile with template,Public,No
Profiles,POST,/api/profiles/p/:profileId/populate-basic,Populate basic information,Private,Yes
Profiles,POST,/api/profiles/p/sync-all,Bulk sync all personal profiles,Private,Yes
Profiles,POST,/api/profiles/:profileId/migrate-to-unavailability,Migrate to unavailability system,Public,No
Profiles,POST,/api/profiles/:profileId/availability,Set availability,Public,No
Profiles,PATCH,/api/profiles/:profileId/availability,Update availability,Public,No
Profiles,GET,/api/profiles/:profileId/availability,Get availability,Public,No
Profiles,GET,/api/profiles/:profileId/availability/slots,Get available slots,Public,No
Profiles,POST,/api/profiles/:profileId/unavailability,Set unavailability,Public,No
Profiles,PATCH,/api/profiles/:profileId/unavailability,Update unavailability,Public,No
Profiles,GET,/api/profiles/:profileId/unavailability,Get unavailability,Public,No
Profiles,POST,/api/profiles/:profileId/scans,Create scan for profile,Private,Yes
Profiles,GET,/api/profiles/:profileId/scans,Get profile scans,Private,Yes
Profiles,GET,/api/profiles/:profileId/scans/stats,Get scan statistics,Private,Yes
Profiles,GET,/api/profiles/scans/:scanId,Get scan by ID,Private,Yes
Profiles,PUT,/api/profiles/scans/:scanId,Update scan,Private,Yes
Profiles,DELETE,/api/profiles/scans/:scanId,Delete scan,Private,Yes
Tasks,GET,/api/tasks/,Get user tasks,Private,Yes
Tasks,POST,/api/tasks/,Create task,Private,Yes
Tasks,GET,/api/tasks/:id,Get task by ID,Private,Yes
Tasks,PUT,/api/tasks/:id,Update task,Private,Yes
Tasks,DELETE,/api/tasks/:id,Delete task,Private,Yes
Tasks,GET,/api/tasks/:id/settings,Get task settings,Private,Yes
Tasks,PUT,/api/tasks/:id/settings,Update task settings,Private,Yes
Tasks,GET,/api/tasks/visible/:userId,Get visible tasks,Private,Yes
Tasks,POST,/api/tasks/:id/like,Like task,Private,Yes
Tasks,POST,/api/tasks/:id/subtasks,Add subtask,Private,Yes
Tasks,PUT,/api/tasks/:id/subtasks/:subTaskIndex,Update subtask,Private,Yes
Tasks,DELETE,/api/tasks/:id/subtasks/:subTaskIndex,Delete subtask,Private,Yes
Tasks,POST,/api/tasks/:id/comments,Add comment,Private,Yes
Tasks,POST,/api/tasks/:id/comments/:commentIndex/like,Like comment,Private,Yes
Tasks,DELETE,/api/tasks/:id/comments/:commentIndex/like,Unlike comment,Private,Yes
Tasks,POST,/api/tasks/:id/attachments,Add attachment,Private,Yes
Tasks,DELETE,/api/tasks/:id/attachments/:attachmentIndex,Remove attachment,Private,Yes
Vault,GET,/api/vault/,Get user's vault,Private,Yes
Vault,DELETE,/api/vault/,Delete user's vault,Private,Yes
Vault,DELETE,/api/vault/clear,Clear all vault items,Private,Yes
Vault,GET,/api/vault/items,Get all vault items with filtering and pagination,Private,Yes
Vault,POST,/api/vault/items,Add new vault item,Private,Yes
Vault,GET,/api/vault/items/:itemId,Get vault item by ID,Private,Yes
Vault,PUT,/api/vault/items/:itemId,Update vault item,Private,Yes
Vault,DELETE,/api/vault/items/:itemId,Delete vault item,Private,Yes
Vault,GET,/api/vault/items/:itemId/audit-trail,Get audit trail for item,Private,Yes
Vault,GET,/api/vault/items/:itemId/versions,Get item versions,Private,Yes
Vault,POST,/api/vault/items/:itemId/versions/restore,Restore item version,Private,Yes
Vault,GET,/api/vault/categories,Get all categories with pagination and search,Private,Yes
Vault,POST,/api/vault/categories,Create new category,Private,Yes
Vault,GET,/api/vault/categories/:categoryId/items,Get items by category,Private,Yes
Vault,GET,/api/vault/subcategories,Get all subcategories,Private,Yes
Vault,POST,/api/vault/subcategories,Create new subcategory,Private,Yes
Vault,GET,/api/vault/subcategories/nested,Get nested subcategories,Private,Yes
Vault,POST,/api/vault/subcategories/move,Move subcategory,Private,Yes
Vault,DELETE,/api/vault/subcategories,Delete subcategory,Private,Yes
Vault,GET,/api/vault/subcategories/:subcategoryId/items,Get items by subcategory,Private,Yes
Vault,GET,/api/vault/settings/:profileId,Get vault settings for profile,Private,Yes
Vault,PUT,/api/vault/settings/:profileId,Update vault settings for profile,Private,Yes
Vault,GET,/api/vault/access-summary/:profileId,Get vault access summary,Private,Yes
Vault,POST,/api/vault/search,Advanced search,Private,Yes
Vault,GET,/api/vault/analytics/:profileId,Get vault analytics,Private,Yes
Vault,POST,/api/vault/batch/update,Batch update items,Private,Yes
Vault,POST,/api/vault/batch/delete,Batch delete items,Private,Yes
Vault,POST,/api/vault/batch/move,Batch move items,Private,Yes 