import Joi from 'joi';

// Helper function for ObjectId validation
const objectId = () => Joi.string().pattern(/^[0-9a-fA-F]{24}$/);

// Coordinates validation schema
const coordinatesSchema = Joi.object({
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  accuracy: Joi.number().min(0).optional()
});

// Base location validation schema
export const createLocationSchema = Joi.object({
  // Required fields
  name: Joi.string().required().trim().max(100).messages({
    'string.empty': 'Name is required',
    'string.max': 'Name must be 100 characters or less'
  }),

  // Either address or coordinates must be provided
  address: Joi.string().trim().max(255).optional(),
  coordinates: coordinatesSchema.optional(),

  // Boolean fields
  useCoordinates: Joi.boolean().default(false),
  isPrivate: Joi.boolean().default(true),

  // Enum fields
  visibility: Joi.string().valid('public', 'private', 'connections_only').default('private'),
  category: Joi.string().valid('home', 'work', 'education', 'entertainment', 'health', 'travel', 'other').default('other'),

  // Optional associations
  profileId: objectId().optional(),

  // Optional metadata
  tags: Joi.array().items(Joi.string().trim()).default([]),
  notes: Joi.string().trim().max(500).optional(),

  // Sharing
  sharedWith: Joi.array().items(objectId()).optional(),

  // Custom metadata
  metadata: Joi.object().optional()
}).custom((value, helpers) => {
  // Custom validation: Either address or coordinates must be provided
  const hasAddress = value.address && value.address.trim() !== '';
  const hasCoordinates = value.coordinates && 
    typeof value.coordinates.latitude === 'number' && 
    typeof value.coordinates.longitude === 'number';

  if (!hasAddress && !hasCoordinates) {
    return helpers.error('custom.missing-location', {
      message: 'Either address or coordinates must be provided'
    });
  }

  // If useCoordinates is true, coordinates must be provided
  if (value.useCoordinates && !hasCoordinates) {
    return helpers.error('custom.coordinates-required', {
      message: 'Coordinates must be provided when useCoordinates is true'
    });
  }

  return value;
});

// Update location validation schema (allows partial updates)
export const updateLocationSchema = Joi.object({
  name: Joi.string().trim().max(100).optional(),
  address: Joi.string().trim().max(255).optional(),
  coordinates: coordinatesSchema.optional(),
  useCoordinates: Joi.boolean().optional(),
  isPrivate: Joi.boolean().optional(),
  visibility: Joi.string().valid('public', 'private', 'connections_only').optional(),
  category: Joi.string().valid('home', 'work', 'education', 'entertainment', 'health', 'travel', 'other').optional(),
  profileId: objectId().optional(),
  tags: Joi.array().items(Joi.string().trim()).optional(),
  notes: Joi.string().trim().max(500).optional(),
  sharedWith: Joi.array().items(objectId()).optional(),
  metadata: Joi.object().optional()
}).min(1).messages({
  'object.min': 'At least one field must be provided for update'
});

// Share/unshare location validation schema
export const shareLocationSchema = Joi.object({
  userIds: Joi.array().items(objectId()).min(1).required().messages({
    'array.min': 'At least one user ID must be provided',
    'array.base': 'userIds must be an array'
  })
});

// Nearby locations query validation schema
export const nearbyLocationsQuerySchema = Joi.object({
  latitude: Joi.number().min(-90).max(90).required(),
  longitude: Joi.number().min(-180).max(180).required(),
  maxDistance: Joi.number().min(1).max(100000).default(10000) // 1m to 100km
});

// Get locations query validation schema
export const getLocationsQuerySchema = Joi.object({
  profileId: objectId().optional(),
  category: Joi.string().valid('home', 'work', 'education', 'entertainment', 'health', 'travel', 'other').optional(),
  visibility: Joi.string().valid('public', 'private', 'connections_only').optional(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  search: Joi.string().trim().max(100).optional()
});

// Location ID validation schema
export const locationIdSchema = Joi.object({
  id: objectId().required().messages({
    'string.pattern.base': 'Invalid location ID format'
  })
}); 