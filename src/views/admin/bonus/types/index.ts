/**
 * Admin Bonus Management Types
 * Comprehensive TypeScript interfaces for bonus management in admin interface
 */

import { ObjectId } from 'mongoose';

// Base interfaces extending from backend
export interface AdminBonusFilters {
  page?: number;
  limit?: number;
  type?: BonusType;
  status?: BonusStatus;
  search?: string;
  campaignId?: string;
  createdBy?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
  startDate?: string;
  endDate?: string;
}

export interface AdminBonusFormData {
  name: string;
  description: string;
  type: BonusType;
  amount: number;
  isPercentage?: boolean;
  maxAmount?: number;
  minAmount?: number;
  status?: BonusStatus;
  isGlobal?: boolean;
  maxUsagePerUser?: number;
  maxTotalUsage?: number;
  startDate?: string;
  endDate?: string;
  campaignId?: string;
  eligibilityRules?: BonusEligibilityRule[];
  triggerEvent?: string;
  relatedActivityType?: string;
  relatedBadgeId?: string;
  relatedMilestoneLevel?: string;
}

export interface AdminBonusDistributionRequest {
  bonusId: string;
  profileId?: string;
  profileIds?: string[];
  reason?: string;
  metadata?: Record<string, any>;
}

export interface AdminCampaignFormData {
  name: string;
  description: string;
  startDate: string;
  endDate: string;
  bonusIds?: string[];
  isGlobal?: boolean;
  maxParticipants?: number;
  budgetLimit?: number;
  targetAudience?: {
    userLevels?: string[];
    countries?: string[];
    minProfileAge?: number;
    maxProfileAge?: number;
    customCriteria?: Record<string, any>;
  };
}

// Enums (matching backend)
export enum BonusType {
  REFERRAL = 'REFERRAL',
  MILESTONE = 'MILESTONE',
  ACTIVITY = 'ACTIVITY',
  MANUAL = 'MANUAL',
  CAMPAIGN = 'CAMPAIGN',
  WELCOME = 'WELCOME',
  LOYALTY = 'LOYALTY',
  BADGE_ACHIEVEMENT = 'BADGE_ACHIEVEMENT',
  DAILY_LOGIN = 'DAILY_LOGIN',
  STREAK = 'STREAK',
  PERFORMANCE = 'PERFORMANCE',
  SEASONAL = 'SEASONAL'
}

export enum BonusStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  EXPIRED = 'EXPIRED',
  SCHEDULED = 'SCHEDULED',
  PAUSED = 'PAUSED'
}

export enum BonusDistributionStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

export enum BonusEligibilityType {
  USER_LEVEL = 'USER_LEVEL',
  ACTIVITY_COUNT = 'ACTIVITY_COUNT',
  MYPTS_BALANCE = 'MYPTS_BALANCE',
  PROFILE_AGE = 'PROFILE_AGE',
  REFERRAL_COUNT = 'REFERRAL_COUNT',
  BADGE_COUNT = 'BADGE_COUNT',
  CUSTOM = 'CUSTOM'
}

export enum CampaignStatus {
  DRAFT = 'DRAFT',
  ACTIVE = 'ACTIVE',
  PAUSED = 'PAUSED',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED'
}

// Detailed interfaces
export interface BonusEligibilityRule {
  ruleType: BonusEligibilityType;
  operator: 'gte' | 'lte' | 'eq' | 'ne' | 'between';
  value: number | string | [number, number];
  description: string;
  isActive?: boolean;
}

export interface AdminBonus {
  _id: string;
  name: string;
  description: string;
  type: BonusType;
  amount: number;
  isPercentage: boolean;
  maxAmount?: number;
  minAmount?: number;
  status: BonusStatus;
  eligibilityRules: BonusEligibilityRule[];
  isGlobal: boolean;
  targetProfileIds?: string[];
  maxUsagePerUser?: number;
  maxTotalUsage?: number;
  currentUsageCount: number;
  startDate?: string;
  endDate?: string;
  campaignId?: string;
  triggerEvent?: string;
  relatedActivityType?: string;
  relatedBadgeId?: string;
  relatedMilestoneLevel?: string;
  createdBy: {
    _id: string;
    username: string;
    email: string;
  };
  lastModifiedBy?: {
    _id: string;
    username: string;
    email: string;
  };
  totalDistributed: number;
  totalAmount: number;
  avgAmountPerUser: number;
  createdAt: string;
  updatedAt: string;
}

export interface AdminBonusTransaction {
  _id: string;
  bonusId: {
    _id: string;
    name: string;
    type: BonusType;
    description: string;
  };
  profileId: {
    _id: string;
    profileInformation: {
      username: string;
    };
  };
  amount: number;
  description: string;
  distributionStatus: BonusDistributionStatus;
  myPtsTransactionId?: string;
  errorMessage?: string;
  metadata?: Record<string, any>;
  distributedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface AdminBonusCampaign {
  _id: string;
  name: string;
  description: string;
  status: CampaignStatus;
  startDate: string;
  endDate: string;
  bonusIds: string[];
  isGlobal: boolean;
  targetAudience?: {
    userLevels?: string[];
    countries?: string[];
    minProfileAge?: number;
    maxProfileAge?: number;
    customCriteria?: Record<string, any>;
  };
  maxParticipants?: number;
  currentParticipants: number;
  budgetLimit?: number;
  currentSpent: number;
  totalBonusesDistributed: number;
  totalMyPtsAwarded: number;
  conversionRate: number;
  participationRate: number;
  createdBy: {
    _id: string;
    username: string;
    email: string;
  };
  lastModifiedBy?: {
    _id: string;
    username: string;
    email: string;
  };
  createdAt: string;
  updatedAt: string;
}

export interface AdminBonusAnalytics {
  profileId?: string;
  period: 'day' | 'week' | 'month' | 'year' | 'all';
  totalBonusesEarned: number;
  totalMyPtsFromBonuses: number;
  bonusesByType: Record<BonusType, number>;
  bonusesByMonth: Array<{
    month: string;
    count: number;
    amount: number;
  }>;
  topBonuses: Array<{
    bonusId: string;
    name: string;
    timesEarned: number;
    totalAmount: number;
  }>;
  uniqueUsersRewarded: number;
  averageAmountPerUser: number;
  retentionRate: number;
}

export interface AdminBonusDistributionStats {
  pending: number;
  processing: number;
  completed: number;
  failed: number;
  cancelled: number;
  totalAmount: number;
}

export interface AdminBonusEligibilityResult {
  isEligible: boolean;
  reasons: string[];
  calculatedAmount?: number;
  restrictions?: {
    usageLimit?: {
      current: number;
      max: number;
    };
    dateRange?: {
      start?: string;
      end?: string;
    };
    rules?: Array<{
      rule: string;
      passed: boolean;
      reason?: string;
    }>;
  };
}

// API Response interfaces
export interface AdminBonusListResponse {
  success: boolean;
  data: {
    bonuses: AdminBonus[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message?: string;
}

export interface AdminBonusDetailResponse {
  success: boolean;
  data: {
    bonus: AdminBonus;
    distributionStats: AdminBonusDistributionStats;
    recentTransactions: AdminBonusTransaction[];
  };
  message?: string;
}

export interface AdminBonusAnalyticsResponse {
  success: boolean;
  data: AdminBonusAnalytics;
  message?: string;
}

export interface AdminBonusTransactionHistoryResponse {
  success: boolean;
  data: {
    transactions: AdminBonusTransaction[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message?: string;
}

export interface AdminCampaignListResponse {
  success: boolean;
  data: {
    campaigns: AdminBonusCampaign[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  };
  message?: string;
}

export interface AdminBonusDistributionResponse {
  success: boolean;
  data?: {
    transaction?: AdminBonusTransaction;
    successCount?: number;
    failureCount?: number;
    results?: Array<{
      profileId: string;
      success: boolean;
      transaction?: AdminBonusTransaction;
      error?: string;
    }>;
  };
  message: string;
}

// Component prop interfaces
export interface AdminBonusTableProps {
  bonuses: AdminBonus[];
  loading?: boolean;
  onEdit: (bonus: AdminBonus) => void;
  onDelete: (bonusId: string) => void;
  onDistribute: (bonus: AdminBonus) => void;
  onViewDetails: (bonusId: string) => void;
}

export interface AdminBonusFormProps {
  initialData?: AdminBonus;
  mode: 'create' | 'edit';
  onSubmit: (data: AdminBonusFormData) => void;
  onCancel: () => void;
  loading?: boolean;
}

export interface AdminBonusDistributionProps {
  bonus: AdminBonus;
  isOpen: boolean;
  onClose: () => void;
  onDistribute: (request: AdminBonusDistributionRequest) => void;
  loading?: boolean;
}

export interface AdminBonusAnalyticsDashboardProps {
  data: AdminBonusAnalytics;
  loading?: boolean;
  onPeriodChange: (period: string) => void;
  selectedPeriod: string;
}

// Utility types
export type AdminBonusSortField = 'name' | 'type' | 'amount' | 'status' | 'createdAt' | 'totalDistributed';
export type AdminBonusSortOrder = 'asc' | 'desc';

export interface AdminBonusTableState {
  filters: AdminBonusFilters;
  selectedBonuses: string[];
  sortField: AdminBonusSortField;
  sortOrder: AdminBonusSortOrder;
}

// Error handling
export interface AdminBonusError {
  code: string;
  message: string;
  field?: string;
  details?: Record<string, any>;
}

export interface AdminBonusValidationErrors {
  [key: string]: string[];
}

// Real-time updates
export interface AdminBonusDistributionUpdate {
  bonusId: string;
  status: BonusDistributionStatus;
  completedCount: number;
  totalCount: number;
  failedCount: number;
  currentProfileId?: string;
}

// Dashboard summary
export interface AdminBonusDashboardSummary {
  totalActiveBonuses: number;
  totalDistributionsToday: number;
  totalMyPtsDistributedToday: number;
  pendingDistributions: number;
  activeCampaigns: number;
  topPerformingBonus: {
    id: string;
    name: string;
    distributionCount: number;
  };
  recentActivity: Array<{
    type: 'bonus_created' | 'bonus_distributed' | 'campaign_started' | 'distribution_failed';
    message: string;
    timestamp: string;
    bonusId?: string;
    profileId?: string;
  }>;
}