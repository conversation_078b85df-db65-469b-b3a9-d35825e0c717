#!/usr/bin/env node

/**
 * Activity Logs API Test Data Seeding Script
 * ==========================================
 * 
 * This script seeds the database with test activity data to verify
 * that the activity logs API endpoints are working correctly.
 */

const mongoose = require('mongoose');
const { AccountActivityModel } = require('./src/models/account-activity.model');
const { ProfileActivityModel } = require('./src/models/gamification/user-activity.model');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/my-profile-dev';

async function connectDB() {
  try {
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
}

async function seedAccountActivities() {
  console.log('🌱 Seeding account activities...');
  
  const testAccountId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b3');
  const testUserId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b5');
  
  const accountActivities = [
    {
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'login',
      category: 'authentication',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
      isRewardable: false,
      isBonusable: false,
      isSystemGenerated: false,
      metadata: {
        source: 'web',
        loginMethod: 'email',
        deviceType: 'desktop'
      },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      source: 'web'
    },
    {
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'profile_updated',
      category: 'account_settings',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12), // 12 hours ago
      isRewardable: true,
      isBonusable: false,
      isSystemGenerated: false,
      metadata: {
        source: 'web',
        fieldUpdated: 'email',
        completionPercentage: 85
      },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      source: 'web'
    },
    {
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'profile_created',
      category: 'profile_creation',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 6), // 6 hours ago
      isRewardable: true,
      isBonusable: true,
      isSystemGenerated: false,
      metadata: {
        source: 'mobile',
        profileType: 'business',
        templateId: 'business-template-1'
      },
      ipAddress: '********',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
      source: 'mobile'
    }
  ];

  try {
    await AccountActivityModel.deleteMany({ accountId: testAccountId });
    await AccountActivityModel.insertMany(accountActivities);
    console.log(`✅ Created ${accountActivities.length} account activities`);
  } catch (error) {
    console.error('❌ Error seeding account activities:', error);
  }
}

async function seedProfileActivities() {
  console.log('🌱 Seeding profile activities...');
  
  const testProfileId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b4');
  const testAccountId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b3');
  const testUserId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b5');
  
  const profileActivities = [
    {
      profileId: testProfileId,
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'mypts_earned',
      category: 'mypts_activities',
      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
      isRewardable: true,
      isBonusable: false,
      isSystemGenerated: false,
      visibility: 'public',
      myPtsEarned: 50,
      myPtsSpent: 0,
      metadata: {
        source: 'web',
        reason: 'Profile completion milestone',
        milestoneLevel: 'bronze'
      },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      source: 'web'
    },
    {
      profileId: testProfileId,
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'connection_made',
      category: 'social_interactions',
      timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
      isRewardable: true,
      isBonusable: true,
      isSystemGenerated: false,
      visibility: 'connections_only',
      myPtsEarned: 25,
      myPtsSpent: 0,
      metadata: {
        source: 'mobile',
        connectedProfileId: '60f7b3b3b3b3b3b3b3b3b3b6',
        connectionType: 'direct',
        connectionMethod: 'qr'
      },
      ipAddress: '********',
      userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X)',
      source: 'mobile'
    },
    {
      profileId: testProfileId,
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'profile_shared',
      category: 'profile_updates',
      timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
      isRewardable: false,
      isBonusable: false,
      isSystemGenerated: false,
      visibility: 'public',
      myPtsEarned: 0,
      myPtsSpent: 0,
      metadata: {
        source: 'web',
        shareMethod: 'link',
        platform: 'social_media'
      },
      ipAddress: '*************',
      userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      source: 'web'
    },
    {
      profileId: testProfileId,
      accountId: testAccountId,
      userId: testUserId,
      userRole: 'user',
      activityType: 'badge_earned',
      category: 'reward_activities',
      timestamp: new Date(Date.now() - 1000 * 60 * 15), // 15 minutes ago
      isRewardable: true,
      isBonusable: true,
      isSystemGenerated: true,
      visibility: 'public',
      myPtsEarned: 100,
      myPtsSpent: 0,
      badgeEarned: 'First Connection Badge',
      milestoneReached: 'Social Starter',
      metadata: {
        source: 'system',
        badgeType: 'social',
        autoAwarded: true
      },
      source: 'system'
    }
  ];

  try {
    await ProfileActivityModel.deleteMany({ profileId: testProfileId });
    await ProfileActivityModel.insertMany(profileActivities);
    console.log(`✅ Created ${profileActivities.length} profile activities`);
  } catch (error) {
    console.error('❌ Error seeding profile activities:', error);
  }
}

async function verifyData() {
  console.log('🔍 Verifying seeded data...');
  
  const testAccountId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b3');
  const testProfileId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b4');
  
  const accountCount = await AccountActivityModel.countDocuments({ accountId: testAccountId });
  const profileCount = await ProfileActivityModel.countDocuments({ profileId: testProfileId });
  
  console.log(`📊 Account activities: ${accountCount}`);
  console.log(`📊 Profile activities: ${profileCount}`);
  
  if (accountCount > 0 && profileCount > 0) {
    console.log('✅ Data verification successful!');
    console.log('\n📋 Test IDs for PostMan:');
    console.log(`   Account ID: ${testAccountId}`);
    console.log(`   Profile ID: ${testProfileId}`);
    console.log(`   User ID: 60f7b3b3b3b3b3b3b3b3b3b5`);
    console.log('\n🔗 Test these endpoints:');
    console.log(`   GET /api/activity-logs/accounts/${testAccountId}`);
    console.log(`   GET /api/activity-logs/profiles/${testProfileId}`);
    console.log(`   GET /api/activity-logs/profiles/${testProfileId}/mypts`);
  } else {
    console.log('❌ Data verification failed!');
  }
}

async function main() {
  console.log('🚀 Starting Activity Logs API Test Data Seeding...\n');
  
  await connectDB();
  await seedAccountActivities();
  await seedProfileActivities();
  await verifyData();
  
  console.log('\n✅ Seeding completed!');
  process.exit(0);
}

// Handle errors
process.on('unhandledRejection', (error) => {
  console.error('❌ Unhandled rejection:', error);
  process.exit(1);
});

// Run the script
main().catch(error => {
  console.error('❌ Script error:', error);
  process.exit(1);
});