const axios = require('axios');

const BASE_URL = 'http://localhost:3000/api';
const TEST_USER_ID = '507f1f77bcf86cd799439011'; // Replace with a real user ID
const TEST_ENTITY_ID = '507f1f77bcf86cd799439012'; // Replace with a real entity ID

const config = {
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_JWT_TOKEN_HERE' // Replace with actual token
  }
};

async function testRateReviewAPI() {
  console.log('🧪 Testing Rate & Review API...\n');

  try {
    // Test 1: Create a review
    console.log('1. Testing POST /api/reviews - Create Review');
    try {
      const reviewData = {
        entityId: TEST_ENTITY_ID,
        entityType: 'product',
        rating: 5,
        title: 'Excellent Product!',
        content: 'This product exceeded my expectations. Great quality and fast delivery.',
        subject: 'Great Experience with MyProfile',
        reason: 'overall_satisfaction',
        improvementCategories: [
          { category: 'overall_service', isSelected: true },
          { category: 'customer_support', isSelected: false },
          { category: 'speed_efficiency', isSelected: true },
          { category: 'profile_setup_experience', isSelected: false },
          { category: 'ease_of_use_navigation', isSelected: true },
          { category: 'other', isSelected: false }
        ],
        isAnonymous: false,
        displayName: 'John Doe',
        location: 'New York, NY',
        media: [
          {
            type: 'image',
            url: 'https://example.com/review-image.jpg',
            caption: 'Product in use'
          }
        ],
        reviewType: 'product',
        isVerifiedPurchase: true
      };

      const response = await axios.post(`${BASE_URL}/reviews`, reviewData, config);
      console.log('✅ Success:', response.data.status);
      console.log('   Review ID:', response.data.data._id);
      
      const reviewId = response.data.data._id;
      
      // Test 2: Get reviews for entity
      console.log('\n2. Testing GET /api/reviews/entity/product/' + TEST_ENTITY_ID);
      try {
        const response = await axios.get(`${BASE_URL}/reviews/entity/product/${TEST_ENTITY_ID}?page=1&limit=5`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Reviews found:', response.data.data.reviews.length);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 3: Get review by ID
      console.log('\n3. Testing GET /api/reviews/' + reviewId);
      try {
        const response = await axios.get(`${BASE_URL}/reviews/${reviewId}`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Review title:', response.data.data.title);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 4: Vote on review
      console.log('\n4. Testing POST /api/reviews/' + reviewId + '/vote');
      try {
        const response = await axios.post(`${BASE_URL}/reviews/${reviewId}/vote`, {
          isHelpful: true
        }, config);
        console.log('✅ Success:', response.data.status);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 5: Add business response
      console.log('\n5. Testing POST /api/reviews/' + reviewId + '/response');
      try {
        const response = await axios.post(`${BASE_URL}/reviews/${reviewId}/response`, {
          message: 'Thank you for your review! We appreciate your feedback.',
          isPublic: true
        }, config);
        console.log('✅ Success:', response.data.status);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 6: Update review
      console.log('\n6. Testing PUT /api/reviews/' + reviewId);
      try {
        const response = await axios.put(`${BASE_URL}/reviews/${reviewId}`, {
          rating: 4,
          title: 'Great Product (Updated)',
          content: 'This product exceeded my expectations. Great quality and fast delivery. (Updated review)'
        }, config);
        console.log('✅ Success:', response.data.status);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 7: Get review statistics
      console.log('\n7. Testing GET /api/reviews/entity/' + TEST_ENTITY_ID + '/stats');
      try {
        const response = await axios.get(`${BASE_URL}/reviews/entity/${TEST_ENTITY_ID}/stats`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Average rating:', response.data.data.averageRating);
        console.log('   Total reviews:', response.data.data.totalReviews);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 8: Get user's reviews
      console.log('\n8. Testing GET /api/reviews/user/me');
      try {
        const response = await axios.get(`${BASE_URL}/reviews/user/me?page=1&limit=5`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   User reviews found:', response.data.data.reviews.length);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 9: Report review
      console.log('\n9. Testing POST /api/reviews/' + reviewId + '/report');
      try {
        const response = await axios.post(`${BASE_URL}/reviews/${reviewId}/report`, {
          reason: 'inappropriate_content',
          description: 'This review contains inappropriate language'
        }, config);
        console.log('✅ Success:', response.data.status);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 10: Get pending reviews (admin only)
      console.log('\n10. Testing GET /api/reviews/admin/pending');
      try {
        const response = await axios.get(`${BASE_URL}/reviews/admin/pending?page=1&limit=5`, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Pending reviews found:', response.data.data.reviews.length);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 11: Moderate review (admin only)
      console.log('\n11. Testing PATCH /api/reviews/admin/' + reviewId + '/moderate');
      try {
        const response = await axios.patch(`${BASE_URL}/reviews/admin/${reviewId}/moderate`, {
          status: 'approved',
          notes: 'Review approved by moderator'
        }, config);
        console.log('✅ Success:', response.data.status);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 12: Bulk moderate reviews (admin only)
      console.log('\n12. Testing POST /api/reviews/admin/bulk-moderate');
      try {
        const response = await axios.post(`${BASE_URL}/reviews/admin/bulk-moderate`, {
          reviewIds: [reviewId],
          status: 'approved',
          notes: 'Bulk approval'
        }, config);
        console.log('✅ Success:', response.data.status);
        console.log('   Reviews processed:', response.data.data.total);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

      // Test 13: Delete review
      console.log('\n13. Testing DELETE /api/reviews/' + reviewId);
      try {
        const response = await axios.delete(`${BASE_URL}/reviews/${reviewId}`, config);
        console.log('✅ Success:', response.data.status);
      } catch (error) {
        console.log('❌ Error:', error.response?.data?.message || error.message);
      }

    } catch (error) {
      console.log('❌ Error creating review:', error.response?.data?.message || error.message);
    }

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Test with different entity types
async function testDifferentEntityTypes() {
  console.log('\n🔍 Testing different entity types...\n');

  const entityTypes = ['product', 'service', 'profile', 'business', 'event'];
  
  for (const entityType of entityTypes) {
    console.log(`Testing entity type: ${entityType}`);
    try {
      const reviewData = {
        entityId: TEST_ENTITY_ID,
        entityType: entityType,
        rating: 4,
        title: `Great ${entityType}!`,
        content: `This ${entityType} was excellent. Highly recommended.`,
        subject: `Review for ${entityType}`,
        reason: 'general_feedback',
        improvementCategories: [
          { category: 'overall_service', isSelected: true },
          { category: 'other', isSelected: false }
        ],
        isAnonymous: false,
        displayName: 'Test User',
        reviewType: entityType === 'product' ? 'product' : 'experience'
      };

      const response = await axios.post(`${BASE_URL}/reviews`, reviewData, config);
      console.log(`✅ Success: ${entityType} review created`);
      
      // Clean up - delete the test review
      const reviewId = response.data.data._id;
      await axios.delete(`${BASE_URL}/reviews/${reviewId}`, config);
      
    } catch (error) {
      console.log(`❌ Error with ${entityType}:`, error.response?.data?.message || error.message);
    }
  }
}

// Test filtering and sorting
async function testFilteringAndSorting() {
  console.log('\n🔍 Testing filtering and sorting...\n');

  try {
    // Test different filters
    const filters = [
      '?rating=5',
      '?isVerifiedPurchase=true',
      '?hasResponse=true',
      '?hasMedia=true',
      '?sortBy=rating&sortOrder=desc',
      '?sortBy=helpful&sortOrder=desc',
      '?sortBy=recent&sortOrder=desc'
    ];

    for (const filter of filters) {
      console.log(`Testing filter: ${filter}`);
      try {
        const response = await axios.get(`${BASE_URL}/reviews/entity/product/${TEST_ENTITY_ID}${filter}`, config);
        console.log(`✅ Success: ${response.data.data.reviews.length} reviews found`);
      } catch (error) {
        console.log(`❌ Error:`, error.response?.data?.message || error.message);
      }
    }
  } catch (error) {
    console.error('❌ Filtering test failed:', error.message);
  }
}

// Run all tests
async function runAllTests() {
  await testRateReviewAPI();
  await testDifferentEntityTypes();
  await testFilteringAndSorting();
  
  console.log('\n🎉 All Rate & Review API tests completed!');
}

runAllTests(); 