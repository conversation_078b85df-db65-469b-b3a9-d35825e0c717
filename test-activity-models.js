#!/usr/bin/env node

/**
 * Quick test to verify activity models static methods are working
 */

const mongoose = require('mongoose');
const { AccountActivityModel } = require('./dist/models/account-activity.model');
const { ProfileActivityModel } = require('./dist/models/gamification/user-activity.model');

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/my-profile-dev';

async function testModels() {
  try {
    console.log('🔌 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Connected to MongoDB');

    // Test AccountActivityModel static methods
    console.log('\n🧪 Testing AccountActivityModel static methods...');
    const accountMethods = [
      'createActivity',
      'findByAccount', 
      'findByUser',
      'findByTimeRange',
      'getActivityStats',
      'getAggregatedData'
    ];

    accountMethods.forEach(method => {
      if (typeof AccountActivityModel[method] === 'function') {
        console.log(`✅ AccountActivityModel.${method} exists`);
      } else {
        console.log(`❌ AccountActivityModel.${method} is missing`);
      }
    });

    // Test ProfileActivityModel static methods
    console.log('\n🧪 Testing ProfileActivityModel static methods...');
    const profileMethods = [
      'createActivity',
      'findByProfile',
      'findByUser', 
      'getMyPtsActivities',
      'getActivityStats',
      'getAggregatedData'
    ];

    profileMethods.forEach(method => {
      if (typeof ProfileActivityModel[method] === 'function') {
        console.log(`✅ ProfileActivityModel.${method} exists`);
      } else {
        console.log(`❌ ProfileActivityModel.${method} is missing`);
      }
    });

    // Test basic database queries
    console.log('\n🔍 Testing database queries...');
    
    try {
      const accountCount = await AccountActivityModel.countDocuments({});
      console.log(`📊 Total account activities in DB: ${accountCount}`);
    } catch (error) {
      console.log('❌ Error counting account activities:', error.message);
    }

    try {
      const profileCount = await ProfileActivityModel.countDocuments({});
      console.log(`📊 Total profile activities in DB: ${profileCount}`);
    } catch (error) {
      console.log('❌ Error counting profile activities:', error.message);
    }

    // Test one of the static methods with real data
    if (typeof ProfileActivityModel.findByProfile === 'function') {
      console.log('\n🧪 Testing ProfileActivityModel.findByProfile method...');
      try {
        const testProfileId = new mongoose.Types.ObjectId('60f7b3b3b3b3b3b3b3b3b3b4');
        const activities = await ProfileActivityModel.findByProfile(testProfileId, {}, 'admin', testProfileId);
        console.log(`✅ findByProfile returned ${activities.length} activities`);
        
        if (activities.length > 0) {
          const firstActivity = activities[0];
          console.log(`📝 Sample activity: ${firstActivity.activityType} (${firstActivity.category})`);
        }
      } catch (error) {
        console.log('❌ Error testing findByProfile:', error.message);
      }
    }

    console.log('\n✅ Model testing completed!');
    
  } catch (error) {
    console.error('❌ Test error:', error);
  } finally {
    await mongoose.disconnect();
    console.log('🔌 Disconnected from MongoDB');
    process.exit(0);
  }
}

testModels().catch(error => {
  console.error('❌ Script error:', error);
  process.exit(1);
});